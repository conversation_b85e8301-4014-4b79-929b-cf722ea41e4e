# Quick Start Guide - RAGFlow AI Chat Integration

## 🚀 Get Started in 5 Minutes

### Prerequisites
- Flutter SDK 3.0+
- RAGFlow account with API access
- Internet connection

## 📋 Step-by-Step Setup

### 1. <PERSON>lone and Setup Project
```bash
# Navigate to project directory
cd ChatRagflowintegrate

# Install dependencies
flutter pub get
```

### 2. Get RAGFlow Credentials

#### Option A: RAGFlow Cloud
1. Visit [RAGFlow Cloud](https://ragflow.io)
2. Sign up or log in to your account
3. Create a new Agent in the dashboard
4. Copy your API Key from settings
5. Note your Agent ID

#### Option B: Self-Hosted RAGFlow
1. Access your RAGFlow instance
2. Log in to the admin panel
3. Create or select an Agent
4. Generate API key
5. Note your server URL and Agent ID

### 3. Configure Environment Variables

#### For macOS/Linux:
```bash
export API_KEY="your-ragflow-api-key-here"
export API_BASE_URL="https://api.ragflow.io"
export AGENT_ID="your-agent-id-here"
```

#### For Windows:
```cmd
set API_KEY=your-ragflow-api-key-here
set API_BASE_URL=https://api.ragflow.io
set AGENT_ID=your-agent-id-here
```

### 4. Run the Application

#### Option 1: RAGFlow-Connected Version (Recommended)
```bash
flutter run -d web-server --web-port 8082 lib/main_ragflow.dart
```
Access at: http://localhost:8082

#### Option 2: Demo Version (No API Required)
```bash
flutter run -d web-server --web-port 8080 lib/main_simple.dart
```
Access at: http://localhost:8080

#### Option 3: Full Production Version
```bash
flutter run -d web-server --web-port 8081 lib/main.dart
```
Access at: http://localhost:8081

## ✅ Verification

### Successful Connection
You should see:
- ✅ "Connected to RAGFlow API" message
- Green "Connected" status indicator
- Welcome message from your AI agent
- Enabled message input field

### Configuration Issues
If you see warnings:
- ⚠️ "Configuration Required" - Set environment variables
- ❌ "Failed to connect" - Check credentials and network

## 🎯 Quick Test

Once connected, try these test messages:

1. **Basic Chat**: "Hello, how are you?"
2. **Knowledge Query**: Ask about your RAGFlow knowledge base
3. **Help**: "What can you help me with?"

## 🔧 Troubleshooting

### Common Issues

| Issue | Solution |
|-------|----------|
| "Configuration Required" | Set API_KEY, API_BASE_URL, AGENT_ID |
| "HTTP 401 Unauthorized" | Check API key validity |
| "HTTP 404 Not Found" | Verify Agent ID exists |
| "Connection timeout" | Check internet and server URL |

### Debug Mode
Enable detailed logging:
```bash
export DEBUG_MODE="true"
```

### Check Configuration
View current settings in the app's debug panel or check environment:
```bash
echo $API_KEY
echo $API_BASE_URL  
echo $AGENT_ID
```

## 📱 Mobile Development

### Android
```bash
flutter run -d android
```

### iOS
```bash
flutter run -d ios
```

## 🧪 Testing

### Run All Tests
```bash
flutter test
```

### Run Specific Tests
```bash
# Integration tests
flutter test test/integration/

# Security tests  
flutter test test/security/

# Performance tests
flutter test test/integration/performance_edge_case_test.dart
```

## 🚀 Production Deployment

### Web Deployment
```bash
flutter build web
# Deploy contents of build/web/ to your web server
```

### Mobile App Stores
```bash
# Android
flutter build apk --release
flutter build appbundle --release

# iOS  
flutter build ios --release
```

## 📚 Additional Resources

- **Full Documentation**: See `RAGFLOW_INTEGRATION_SUMMARY.md`
- **API Reference**: Check RAGFlow documentation
- **Flutter Docs**: https://docs.flutter.dev
- **Project README**: See `README.md`

## 🆘 Support

### Getting Help
1. Check the troubleshooting section above
2. Review error messages in the app
3. Enable debug mode for detailed logs
4. Verify RAGFlow service status
5. Check network connectivity

### Common Commands
```bash
# Hot reload (while running)
r

# Hot restart (while running)  
R

# Quit application (while running)
q

# Flutter doctor (check setup)
flutter doctor

# Clean and rebuild
flutter clean && flutter pub get
```

## 🎉 Success!

Once you see the "Connected to RAGFlow API" message, you're ready to:
- Chat with your AI agent
- Access your knowledge base
- Use all RAGFlow features
- Deploy to production

**Congratulations! Your RAGFlow AI Chat Integration is now running! 🚀**

---

*Need help? Check the full documentation in `RAGFLOW_INTEGRATION_SUMMARY.md`*
