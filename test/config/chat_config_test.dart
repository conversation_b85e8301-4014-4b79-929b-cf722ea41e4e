import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:ai_chat_app/config/chat_config.dart';

void main() {
  group('ChatConfig Tests', () {
    // Store original environment variables
    late Map<String, String> originalEnv;

    setUp(() {
      originalEnv = Map.from(Platform.environment);
    });

    tearDown(() {
      // Restore original environment variables
      Platform.environment.clear();
      Platform.environment.addAll(originalEnv);
    });

    group('Environment Variable Handling', () {
      test('should use primary environment variable when available', () {
        Platform.environment['API_BASE_URL'] = 'https://primary.example.com';
        Platform.environment['CHAT_API_BASE_URL'] = 'https://secondary.example.com';
        
        expect(ChatConfig.baseUrl, equals('https://primary.example.com'));
      });

      test('should fallback to secondary environment variable', () {
        Platform.environment.remove('API_BASE_URL');
        Platform.environment['CHAT_API_BASE_URL'] = 'https://secondary.example.com';
        
        expect(ChatConfig.baseUrl, equals('https://secondary.example.com'));
      });

      test('should use default value when no environment variables set', () {
        Platform.environment.remove('API_BASE_URL');
        Platform.environment.remove('CHAT_API_BASE_URL');
        
        expect(ChatConfig.baseUrl, equals('https://api.ragflow.io'));
      });

      test('should trim whitespace from environment variables', () {
        Platform.environment['API_KEY'] = '  test-api-key  ';
        
        expect(ChatConfig.apiKey, equals('test-api-key'));
      });
    });

    group('Integer Environment Variables', () {
      test('should parse valid integer values', () {
        Platform.environment['REQUEST_TIMEOUT_SECONDS'] = '45';
        
        expect(ChatConfig.requestTimeoutSeconds, equals(45));
      });

      test('should use default for invalid integer values', () {
        Platform.environment['REQUEST_TIMEOUT_SECONDS'] = 'invalid';
        
        expect(ChatConfig.requestTimeoutSeconds, equals(30)); // default value
      });

      test('should enforce minimum bounds', () {
        Platform.environment['REQUEST_TIMEOUT_SECONDS'] = '1'; // below minimum of 5
        
        expect(ChatConfig.requestTimeoutSeconds, equals(5)); // minimum value
      });

      test('should enforce maximum bounds', () {
        Platform.environment['REQUEST_TIMEOUT_SECONDS'] = '500'; // above maximum of 300
        
        expect(ChatConfig.requestTimeoutSeconds, equals(300)); // maximum value
      });
    });

    group('Boolean Environment Variables', () {
      test('should parse true values correctly', () {
        final trueValues = ['true', '1', 'yes', 'on', 'TRUE', 'YES', 'ON'];
        
        for (final value in trueValues) {
          Platform.environment['DEBUG_MODE'] = value;
          expect(ChatConfig.isDebugMode, isTrue, reason: 'Failed for value: $value');
        }
      });

      test('should parse false values correctly', () {
        final falseValues = ['false', '0', 'no', 'off', 'FALSE', 'NO', 'OFF'];
        
        for (final value in falseValues) {
          Platform.environment['DEBUG_MODE'] = value;
          expect(ChatConfig.isDebugMode, isFalse, reason: 'Failed for value: $value');
        }
      });

      test('should use default for invalid boolean values', () {
        Platform.environment['DEBUG_MODE'] = 'invalid';
        
        expect(ChatConfig.isDebugMode, isFalse); // default value
      });
    });

    group('Configuration Validation', () {
      test('should validate complete configuration', () {
        Platform.environment['API_KEY'] = 'valid-api-key-123';
        Platform.environment['API_BASE_URL'] = 'https://api.example.com';
        Platform.environment['AGENT_ID'] = 'valid-agent-id';
        
        final result = ChatConfig.validateConfiguration();
        
        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('should detect missing API key', () {
        Platform.environment.remove('API_KEY');
        Platform.environment.remove('CHAT_API_KEY');
        
        final result = ChatConfig.validateConfiguration();
        
        expect(result.isValid, isFalse);
        expect(result.errors, contains(contains('API key is required')));
      });

      test('should detect invalid base URL', () {
        Platform.environment['API_BASE_URL'] = 'invalid-url';
        
        final result = ChatConfig.validateConfiguration();
        
        expect(result.isValid, isFalse);
        expect(result.errors, contains(contains('Base URL is not a valid URL')));
      });

      test('should warn about short API key', () {
        Platform.environment['API_KEY'] = 'short';
        Platform.environment['API_BASE_URL'] = 'https://api.example.com';
        Platform.environment['AGENT_ID'] = 'valid-agent';
        
        final result = ChatConfig.validateConfiguration();
        
        expect(result.isValid, isTrue);
        expect(result.warnings, contains(contains('API key seems too short')));
      });

      test('should warn about HTTP instead of HTTPS', () {
        Platform.environment['API_KEY'] = 'valid-api-key-123';
        Platform.environment['API_BASE_URL'] = 'http://api.example.com';
        Platform.environment['AGENT_ID'] = 'valid-agent';
        Platform.environment['VALIDATE_SSL_CERTIFICATES'] = 'true';
        
        final result = ChatConfig.validateConfiguration();
        
        expect(result.isValid, isTrue);
        expect(result.warnings, contains(contains('Using HTTP instead of HTTPS')));
      });

      test('should warn about extreme timeout values', () {
        Platform.environment['API_KEY'] = 'valid-api-key-123';
        Platform.environment['API_BASE_URL'] = 'https://api.example.com';
        Platform.environment['AGENT_ID'] = 'valid-agent';
        Platform.environment['REQUEST_TIMEOUT_SECONDS'] = '200';
        
        final result = ChatConfig.validateConfiguration();
        
        expect(result.isValid, isTrue);
        expect(result.warnings, contains(contains('Request timeout is very high')));
      });
    });

    group('Configuration Summary', () {
      test('should provide complete configuration summary', () {
        Platform.environment['API_KEY'] = 'test-api-key-123';
        Platform.environment['API_BASE_URL'] = 'https://api.example.com';
        Platform.environment['AGENT_ID'] = 'test-agent';
        
        final summary = ChatConfig.getConfigurationSummary();
        
        expect(summary['baseUrl'], equals('https://api.example.com'));
        expect(summary['apiKey'], equals('te***********23')); // masked
        expect(summary['defaultAgentId'], equals('test-agent'));
        expect(summary['requestTimeoutSeconds'], isA<int>());
        expect(summary['maxRetries'], isA<int>());
        expect(summary['isConfigured'], isA<bool>());
      });

      test('should include sensitive data when requested', () {
        Platform.environment['API_KEY'] = 'test-api-key-123';
        
        final summary = ChatConfig.getConfigurationSummary(includeSensitive: true);
        
        expect(summary['apiKey'], equals('test-api-key-123')); // not masked
      });

      test('should mask empty values appropriately', () {
        Platform.environment.remove('API_KEY');
        Platform.environment.remove('CHAT_API_KEY');
        
        final summary = ChatConfig.getConfigurationSummary();
        
        expect(summary['apiKey'], equals('<empty>'));
      });
    });

    group('API Endpoints', () {
      test('should construct correct endpoints', () {
        Platform.environment['API_BASE_URL'] = 'https://api.example.com';
        
        expect(ChatConfig.completionsEndpoint, equals('https://api.example.com/completions'));
        expect(ChatConfig.sessionsEndpoint, equals('https://api.example.com/sessions'));
      });
    });

    group('Default Values', () {
      test('should have reasonable default values', () {
        // Clear all environment variables
        Platform.environment.clear();
        
        expect(ChatConfig.requestTimeoutSeconds, equals(30));
        expect(ChatConfig.maxRetries, equals(3));
        expect(ChatConfig.retryDelay, equals(const Duration(seconds: 2)));
        expect(ChatConfig.maxMessageLength, equals(1000));
        expect(ChatConfig.messagesPerPage, equals(50));
        expect(ChatConfig.isDebugMode, isFalse);
        expect(ChatConfig.enableLogging, isTrue);
        expect(ChatConfig.validateSslCertificates, isTrue);
        expect(ChatConfig.maxConcurrentRequests, equals(5));
      });
    });
  });
}
