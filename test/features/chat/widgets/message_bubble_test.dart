import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ai_chat_app/features/chat/widgets/message_bubble.dart';
import 'package:ai_chat_app/features/chat/models/message.dart';

void main() {
  group('MessageBubble', () {
    late Message userMessage;
    late Message assistantMessage;
    late Message errorMessage;
    late Message systemMessage;
    late Message messageWithMetadata;

    setUp(() {
      userMessage = Message(
        id: 'user-1',
        content: 'Hello, how are you?',
        type: MessageType.user,
        status: MessageStatus.sent,
      );

      assistantMessage = Message(
        id: 'assistant-1',
        content: 'I am doing well, thank you for asking!',
        type: MessageType.assistant,
        status: MessageStatus.sent,
      );

      errorMessage = Message(
        id: 'error-1',
        content: 'This message failed to send',
        type: MessageType.user,
        status: MessageStatus.failed,
      );

      systemMessage = Message(
        id: 'system-1',
        content: 'System notification',
        type: MessageType.system,
        status: MessageStatus.sent,
      );

      messageWithMetadata = Message(
        id: 'meta-1',
        content: 'Response with structured data',
        type: MessageType.assistant,
        status: MessageStatus.sent,
        metadata: {
          'reference': {
            'source': 'database',
            'table': 'users',
            'count': 42,
          },
          'param': ['param1', 'param2', 'param3'],
        },
      );
    });

    group('Basic Rendering', () {
      testWidgets('should render user message correctly', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: userMessage),
            ),
          ),
        );

        expect(find.text('Hello, how are you?'), findsOneWidget);
        expect(find.byIcon(Icons.person), findsOneWidget);
      });

      testWidgets('should render assistant message correctly', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: assistantMessage),
            ),
          ),
        );

        expect(find.text('I am doing well, thank you for asking!'), findsOneWidget);
        expect(find.byIcon(Icons.smart_toy), findsOneWidget);
      });

      testWidgets('should render system message correctly', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: systemMessage),
            ),
          ),
        );

        expect(find.text('System notification'), findsOneWidget);
        expect(find.byIcon(Icons.info), findsOneWidget);
      });

      testWidgets('should render error message correctly', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: errorMessage),
            ),
          ),
        );

        expect(find.text('This message failed to send'), findsOneWidget);
        expect(find.byIcon(Icons.person), findsOneWidget);
      });
    });

    group('Message Status', () {
      testWidgets('should show sending indicator for sending message', (tester) async {
        final sendingMessage = userMessage.copyWith(status: MessageStatus.sending);
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: sendingMessage),
            ),
          ),
        );

        expect(find.text('Sending...'), findsOneWidget);
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });

      testWidgets('should show retry button for failed message', (tester) async {
        bool retryPressed = false;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(
                message: errorMessage,
                onRetry: () => retryPressed = true,
              ),
            ),
          ),
        );

        expect(find.text('Retry'), findsOneWidget);
        expect(find.byIcon(Icons.refresh), findsOneWidget);

        await tester.tap(find.text('Retry'));
        await tester.pump();

        expect(retryPressed, true);
      });

      testWidgets('should not show retry button when no callback provided', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: errorMessage),
            ),
          ),
        );

        expect(find.text('Retry'), findsNothing);
      });
    });

    group('Avatar and Timestamp', () {
      testWidgets('should hide avatar when showAvatar is false', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(
                message: userMessage,
                showAvatar: false,
              ),
            ),
          ),
        );

        expect(find.byIcon(Icons.person), findsNothing);
      });

      testWidgets('should show timestamp when showTimestamp is true', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(
                message: userMessage,
                showTimestamp: true,
              ),
            ),
          ),
        );

        // Should find timestamp text (format: HH:MM)
        expect(find.textContaining(':'), findsOneWidget);
      });

      testWidgets('should hide timestamp when showTimestamp is false', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(
                message: userMessage,
                showTimestamp: false,
              ),
            ),
          ),
        );

        // Should not find any colon (timestamp format indicator)
        expect(find.textContaining(':'), findsNothing);
      });
    });

    group('Structured Data', () {
      testWidgets('should render reference data correctly', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: messageWithMetadata),
            ),
          ),
        );

        expect(find.text('Response with structured data'), findsOneWidget);
        expect(find.text('Reference Data'), findsOneWidget);
        expect(find.text('source:'), findsOneWidget);
        expect(find.text('database'), findsOneWidget);
        expect(find.text('table:'), findsOneWidget);
        expect(find.text('users'), findsOneWidget);
        expect(find.text('count:'), findsOneWidget);
        expect(find.text('42'), findsOneWidget);
      });

      testWidgets('should render parameter data correctly', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: messageWithMetadata),
            ),
          ),
        );

        expect(find.text('Parameters'), findsOneWidget);
        expect(find.textContaining('param1'), findsOneWidget);
        expect(find.textContaining('param2'), findsOneWidget);
        expect(find.textContaining('param3'), findsOneWidget);
      });

      testWidgets('should handle empty metadata gracefully', (tester) async {
        final messageWithEmptyMetadata = Message(
          id: 'empty-meta',
          content: 'Message without metadata',
          type: MessageType.assistant,
          status: MessageStatus.sent,
          metadata: {},
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: messageWithEmptyMetadata),
            ),
          ),
        );

        expect(find.text('Message without metadata'), findsOneWidget);
        expect(find.text('Reference Data'), findsNothing);
        expect(find.text('Parameters'), findsNothing);
      });
    });

    group('Interaction', () {
      testWidgets('should show snackbar when long pressing message', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: userMessage),
            ),
          ),
        );

        // Find the GestureDetector for the message
        final gestureDetector = find.byType(GestureDetector).first;

        await tester.longPress(gestureDetector);
        await tester.pumpAndSettle();

        // Look for SnackBar instead of just the text
        expect(find.byType(SnackBar), findsOneWidget);
      });
    });

    group('Message Layout', () {
      testWidgets('should align user messages to the right', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: userMessage),
            ),
          ),
        );

        final row = tester.widget<Row>(find.byType(Row).first);
        expect(row.mainAxisAlignment, MainAxisAlignment.end);
      });

      testWidgets('should align assistant messages to the left', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: assistantMessage),
            ),
          ),
        );

        final row = tester.widget<Row>(find.byType(Row).first);
        expect(row.mainAxisAlignment, MainAxisAlignment.start);
      });
    });

    group('Theme Adaptation', () {
      testWidgets('should adapt to light theme', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData.light(),
            home: Scaffold(
              body: MessageBubble(message: userMessage),
            ),
          ),
        );

        await tester.pumpAndSettle();
        expect(find.byType(MessageBubble), findsOneWidget);
      });

      testWidgets('should adapt to dark theme', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData.dark(),
            home: Scaffold(
              body: MessageBubble(message: userMessage),
            ),
          ),
        );

        await tester.pumpAndSettle();
        expect(find.byType(MessageBubble), findsOneWidget);
      });
    });

    group('Error Handling', () {
      testWidgets('should handle null metadata gracefully', (tester) async {
        final messageWithNullMetadata = Message(
          id: 'null-meta',
          content: 'Message with null metadata',
          type: MessageType.assistant,
          status: MessageStatus.sent,
          metadata: null,
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: messageWithNullMetadata),
            ),
          ),
        );

        expect(find.text('Message with null metadata'), findsOneWidget);
        expect(find.text('Reference Data'), findsNothing);
        expect(find.text('Parameters'), findsNothing);
      });

      testWidgets('should handle malformed reference data', (tester) async {
        final messageWithBadReference = Message(
          id: 'bad-ref',
          content: 'Message with bad reference',
          type: MessageType.assistant,
          status: MessageStatus.sent,
          metadata: {
            'reference': 'not a map',
          },
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: messageWithBadReference),
            ),
          ),
        );

        expect(find.text('Message with bad reference'), findsOneWidget);
        expect(find.text('Reference Data'), findsNothing);
      });

      testWidgets('should handle malformed parameter data', (tester) async {
        final messageWithBadParam = Message(
          id: 'bad-param',
          content: 'Message with bad parameters',
          type: MessageType.assistant,
          status: MessageStatus.sent,
          metadata: {
            'param': 'not a list',
          },
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MessageBubble(message: messageWithBadParam),
            ),
          ),
        );

        expect(find.text('Message with bad parameters'), findsOneWidget);
        expect(find.text('Parameters'), findsNothing);
      });
    });
  });
}