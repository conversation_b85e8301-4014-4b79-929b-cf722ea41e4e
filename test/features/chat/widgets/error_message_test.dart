import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ai_chat_app/features/chat/widgets/error_message.dart';

void main() {
  group('ChatError', () {
    test('should create network error from SocketException', () {
      final exception = Exception('SocketException: Connection failed');
      final error = ChatError.fromException(exception);

      expect(error.type, ChatErrorType.network);
      expect(error.message, 'Network connection failed');
      expect(error.isRetryable, true);
    });

    test('should create timeout error from TimeoutException', () {
      final exception = Exception('TimeoutException: Request timed out');
      final error = ChatError.fromException(exception);

      expect(error.type, ChatErrorType.timeout);
      expect(error.message, 'Request timed out');
      expect(error.isRetryable, true);
    });

    test('should create authentication error from 401 status', () {
      final exception = Exception('401 Unauthorized');
      final error = ChatError.fromException(exception);

      expect(error.type, ChatErrorType.authentication);
      expect(error.message, 'Authentication failed');
      expect(error.isRetryable, false);
    });

    test('should create validation error from 400 status', () {
      final exception = Exception('400 Bad Request: Invalid agent');
      final error = ChatError.fromException(exception);

      expect(error.type, ChatErrorType.validation);
      expect(error.message, 'Invalid request');
      expect(error.isRetryable, false);
    });

    test('should create server error from 500 status', () {
      final exception = Exception('500 Internal Server Error');
      final error = ChatError.fromException(exception);

      expect(error.type, ChatErrorType.server);
      expect(error.message, 'Server error');
      expect(error.isRetryable, true);
    });

    test('should create unknown error for unrecognized exceptions', () {
      final exception = Exception('Some random error');
      final error = ChatError.fromException(exception);

      expect(error.type, ChatErrorType.unknown);
      expect(error.message, 'An unexpected error occurred');
      expect(error.isRetryable, true);
    });

    test('should get correct icon for each error type', () {
      expect(ChatError(type: ChatErrorType.network, message: '').icon,
          Icons.wifi_off);
      expect(ChatError(type: ChatErrorType.authentication, message: '').icon,
          Icons.lock);
      expect(ChatError(type: ChatErrorType.validation, message: '').icon,
          Icons.warning);
      expect(
          ChatError(type: ChatErrorType.server, message: '').icon, Icons.dns);
      expect(ChatError(type: ChatErrorType.timeout, message: '').icon,
          Icons.timer_off);
      expect(ChatError(type: ChatErrorType.unknown, message: '').icon,
          Icons.error);
    });
  });

  group('ErrorMessage Widget', () {
    testWidgets('should display error message with icon', (tester) async {
      final error = ChatError(
        type: ChatErrorType.network,
        message: 'Network error',
        details: 'Check your connection',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ErrorMessage(error: error),
          ),
        ),
      );

      expect(find.text('Network error'), findsOneWidget);
      expect(find.text('Check your connection'), findsOneWidget);
      expect(find.byIcon(Icons.wifi_off), findsOneWidget);
    });

    testWidgets('should show retry button for retryable errors',
        (tester) async {
      bool retryPressed = false;
      final error = ChatError(
        type: ChatErrorType.network,
        message: 'Network error',
        isRetryable: true,
        onRetry: () => retryPressed = true,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ErrorMessage(error: error),
          ),
        ),
      );

      expect(find.text('Retry'), findsOneWidget);

      await tester.tap(find.text('Retry'));
      await tester.pump();

      expect(retryPressed, true);
    });

    testWidgets('should not show retry button for non-retryable errors',
        (tester) async {
      final error = ChatError(
        type: ChatErrorType.authentication,
        message: 'Auth error',
        isRetryable: false,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ErrorMessage(error: error),
          ),
        ),
      );

      expect(find.text('Retry'), findsNothing);
    });

    testWidgets('should show dismiss button when onDismiss is provided',
        (tester) async {
      bool dismissPressed = false;
      final error = ChatError(
        type: ChatErrorType.network,
        message: 'Network error',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ErrorMessage(
              error: error,
              onDismiss: () => dismissPressed = true,
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.close), findsOneWidget);

      await tester.tap(find.byIcon(Icons.close));
      await tester.pump();

      expect(dismissPressed, true);
    });

    testWidgets('should hide details when showDetails is false',
        (tester) async {
      final error = ChatError(
        type: ChatErrorType.network,
        message: 'Network error',
        details: 'Check your connection',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ErrorMessage(
              error: error,
              showDetails: false,
            ),
          ),
        ),
      );

      expect(find.text('Network error'), findsOneWidget);
      expect(find.text('Check your connection'), findsNothing);
    });
  });

  group('InlineErrorMessage Widget', () {
    testWidgets('should display inline error message', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: InlineErrorMessage(
              message: 'Failed to send',
            ),
          ),
        ),
      );

      expect(find.text('Failed to send'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });

    testWidgets('should show retry button when onRetry is provided',
        (tester) async {
      bool retryPressed = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: InlineErrorMessage(
              message: 'Failed to send',
              onRetry: () => retryPressed = true,
            ),
          ),
        ),
      );

      expect(find.text('Retry'), findsOneWidget);

      await tester.tap(find.text('Retry'));
      await tester.pump();

      expect(retryPressed, true);
    });

    testWidgets('should use compact styling when isCompact is true',
        (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: InlineErrorMessage(
              message: 'Failed to send',
              isCompact: true,
            ),
          ),
        ),
      );

      final iconWidget = tester.widget<Icon>(find.byIcon(Icons.error_outline));
      expect(iconWidget.size, 16);
    });
  });

  group('ErrorBanner Widget', () {
    testWidgets('should display error banner', (tester) async {
      final error = ChatError(
        type: ChatErrorType.server,
        message: 'Server error',
        details: 'Try again later',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ErrorBanner(error: error),
          ),
        ),
      );

      expect(find.text('Server error'), findsOneWidget);
      expect(find.text('Try again later'), findsOneWidget);
      expect(find.byIcon(Icons.dns), findsOneWidget);
    });

    testWidgets('should show retry and dismiss buttons', (tester) async {
      bool retryPressed = false;
      bool dismissPressed = false;

      final error = ChatError(
        type: ChatErrorType.network,
        message: 'Network error',
        isRetryable: true,
        onRetry: () => retryPressed = true,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ErrorBanner(
              error: error,
              onDismiss: () => dismissPressed = true,
            ),
          ),
        ),
      );

      expect(find.text('Retry'), findsOneWidget);
      expect(find.byIcon(Icons.close), findsOneWidget);

      await tester.tap(find.text('Retry'));
      await tester.pump();
      expect(retryPressed, true);

      await tester.tap(find.byIcon(Icons.close));
      await tester.pump();
      expect(dismissPressed, true);
    });
  });
}
