import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ai_chat_app/features/chat/widgets/message_bubble.dart';
import 'package:ai_chat_app/features/chat/models/message.dart';

void main() {
  group('Structured Data Formatting Tests', () {
    testWidgets('should display table data from list of maps', (WidgetTester tester) async {
      final message = Message(
        content: 'Here is the data you requested:',
        type: MessageType.assistant,
        metadata: {
          'table': [
            {'name': '<PERSON>', 'age': 30, 'city': 'New York'},
            {'name': '<PERSON>', 'age': 25, 'city': 'Los Angeles'},
            {'name': '<PERSON>', 'age': 35, 'city': 'Chicago'},
          ],
        },
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(message: message),
          ),
        ),
      );

      // Should display table header
      expect(find.text('Table Data'), findsOneWidget);
      
      // Should display column headers
      expect(find.text('age'), findsOneWidget);
      expect(find.text('city'), findsOneWidget);
      expect(find.text('name'), findsOneWidget);
      
      // Should display data
      expect(find.text('John'), findsOneWidget);
      expect(find.text('30'), findsOneWidget);
      expect(find.text('New York'), findsOneWidget);
      expect(find.text('Jane'), findsOneWidget);
      expect(find.text('25'), findsOneWidget);
      expect(find.text('Los Angeles'), findsOneWidget);
    });

    testWidgets('should display structured table with headers and rows', (WidgetTester tester) async {
      final message = Message(
        content: 'Sales report:',
        type: MessageType.assistant,
        metadata: {
          'table': {
            'headers': ['Product', 'Sales', 'Revenue'],
            'rows': [
              ['Widget A', '100', '\$1000'],
              ['Widget B', '150', '\$1500'],
              ['Widget C', '75', '\$750'],
            ],
          },
        },
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(message: message),
          ),
        ),
      );

      // Should display table header
      expect(find.text('Structured Table'), findsOneWidget);
      
      // Should display column headers
      expect(find.text('Product'), findsOneWidget);
      expect(find.text('Sales'), findsOneWidget);
      expect(find.text('Revenue'), findsOneWidget);
      
      // Should display data
      expect(find.text('Widget A'), findsOneWidget);
      expect(find.text('100'), findsOneWidget);
      expect(find.text('\$1000'), findsOneWidget);
    });

    testWidgets('should display key-value table from map', (WidgetTester tester) async {
      final message = Message(
        content: 'Configuration settings:',
        type: MessageType.assistant,
        metadata: {
          'table': {
            'timeout': '30 seconds',
            'retries': '3',
            'endpoint': 'https://api.example.com',
            'version': '1.2.3',
          },
        },
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(message: message),
          ),
        ),
      );

      // Should display table header
      expect(find.text('Key-Value Table'), findsOneWidget);
      
      // Should display key-value pairs
      expect(find.text('timeout:'), findsOneWidget);
      expect(find.text('30 seconds'), findsOneWidget);
      expect(find.text('retries:'), findsOneWidget);
      expect(find.text('3'), findsOneWidget);
      expect(find.text('endpoint:'), findsOneWidget);
      expect(find.text('https://api.example.com'), findsOneWidget);
    });

    testWidgets('should display simple list from array', (WidgetTester tester) async {
      final message = Message(
        content: 'Available options:',
        type: MessageType.assistant,
        metadata: {
          'table': ['Option A', 'Option B', 'Option C', 'Option D'],
        },
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(message: message),
          ),
        ),
      );

      // Should display list header
      expect(find.text('List Data'), findsOneWidget);
      
      // Should display numbered list items
      expect(find.text('1. '), findsOneWidget);
      expect(find.text('Option A'), findsOneWidget);
      expect(find.text('2. '), findsOneWidget);
      expect(find.text('Option B'), findsOneWidget);
      expect(find.text('3. '), findsOneWidget);
      expect(find.text('Option C'), findsOneWidget);
      expect(find.text('4. '), findsOneWidget);
      expect(find.text('Option D'), findsOneWidget);
    });

    testWidgets('should display reference data correctly', (WidgetTester tester) async {
      final message = Message(
        content: 'Based on the following reference:',
        type: MessageType.assistant,
        metadata: {
          'reference': {
            'document': 'User Manual v2.1',
            'section': '3.4 Configuration',
            'page': '42',
            'url': 'https://docs.example.com/manual',
          },
        },
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(message: message),
          ),
        ),
      );

      // Should display reference header
      expect(find.text('Reference Data'), findsOneWidget);
      
      // Should display reference data
      expect(find.text('document:'), findsOneWidget);
      expect(find.text('User Manual v2.1'), findsOneWidget);
      expect(find.text('section:'), findsOneWidget);
      expect(find.text('3.4 Configuration'), findsOneWidget);
      expect(find.text('page:'), findsOneWidget);
      expect(find.text('42'), findsOneWidget);
    });

    testWidgets('should display parameter data correctly', (WidgetTester tester) async {
      final message = Message(
        content: 'Function called with parameters:',
        type: MessageType.assistant,
        metadata: {
          'param': ['user_id=123', 'action=update', 'timestamp=2024-01-15T10:30:00Z'],
        },
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(message: message),
          ),
        ),
      );

      // Should display parameters header
      expect(find.text('Parameters'), findsOneWidget);
      
      // Should display numbered parameters
      expect(find.text('1. '), findsOneWidget);
      expect(find.text('user_id=123'), findsOneWidget);
      expect(find.text('2. '), findsOneWidget);
      expect(find.text('action=update'), findsOneWidget);
      expect(find.text('3. '), findsOneWidget);
      expect(find.text('timestamp=2024-01-15T10:30:00Z'), findsOneWidget);
    });

    testWidgets('should display multiple structured data types together', (WidgetTester tester) async {
      final message = Message(
        content: 'Analysis complete:',
        type: MessageType.assistant,
        metadata: {
          'table': [
            {'metric': 'CPU Usage', 'value': '75%'},
            {'metric': 'Memory Usage', 'value': '60%'},
          ],
          'reference': {
            'source': 'System Monitor',
            'timestamp': '2024-01-15T10:30:00Z',
          },
          'param': ['interval=5min', 'threshold=80%'],
        },
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(message: message),
          ),
        ),
      );

      // Should display all data types
      expect(find.text('Table Data'), findsOneWidget);
      expect(find.text('Reference Data'), findsOneWidget);
      expect(find.text('Parameters'), findsOneWidget);
      
      // Should display table data
      expect(find.text('CPU Usage'), findsOneWidget);
      expect(find.text('75%'), findsOneWidget);
      
      // Should display reference data
      expect(find.text('source:'), findsOneWidget);
      expect(find.text('System Monitor'), findsOneWidget);
      
      // Should display parameters
      expect(find.text('interval=5min'), findsOneWidget);
      expect(find.text('threshold=80%'), findsOneWidget);
    });

    testWidgets('should handle empty or null structured data gracefully', (WidgetTester tester) async {
      final message = Message(
        content: 'No additional data available.',
        type: MessageType.assistant,
        metadata: {
          'table': null,
          'reference': {},
          'param': [],
        },
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(message: message),
          ),
        ),
      );

      // Should only display the main content
      expect(find.text('No additional data available.'), findsOneWidget);
      
      // Should not display empty structured data sections
      expect(find.text('Table Data'), findsNothing);
      expect(find.text('Reference Data'), findsNothing);
      expect(find.text('Parameters'), findsNothing);
    });
  });
}
