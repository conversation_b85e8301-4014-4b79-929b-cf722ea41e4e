import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ai_chat_app/features/chat/widgets/chat_input.dart';

void main() {
  group('ChatInput', () {
    late List<String> sentMessages;

    setUp(() {
      sentMessages = [];
    });

    void onSendMessage(String message) {
      sentMessages.add(message);
    }

    group('Basic Rendering', () {
      testWidgets('should render input field and send button', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChatInput(onSendMessage: onSendMessage),
            ),
          ),
        );

        expect(find.byType(TextField), findsOneWidget);
        expect(find.byIcon(Icons.send_outlined), findsOneWidget);
        expect(find.text('Type your message...'), findsOneWidget);
      });

      testWidgets('should show custom placeholder', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChatInput(
                onSendMessage: onSendMessage,
                placeholder: 'Custom placeholder',
              ),
            ),
          ),
        );

        expect(find.text('Custom placeholder'), findsOneWidget);
      });

      testWidgets('should be disabled when isEnabled is false', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChatInput(
                onSendMessage: onSendMessage,
                isEnabled: false,
              ),
            ),
          ),
        );

        final textField = tester.widget<TextField>(find.byType(TextField));
        expect(textField.enabled, false);
      });

      testWidgets('should show loading indicator when isLoading is true', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChatInput(
                onSendMessage: onSendMessage,
                isLoading: true,
              ),
            ),
          ),
        );

        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        expect(find.byIcon(Icons.send_outlined), findsNothing);
      });
    });

    group('Text Input Behavior', () {
      testWidgets('should update send button when text changes', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChatInput(onSendMessage: onSendMessage),
            ),
          ),
        );

        // Initially should show outlined icon (disabled state)
        expect(find.byIcon(Icons.send_outlined), findsOneWidget);

        // Type some text
        await tester.enterText(find.byType(TextField), 'Hello');
        await tester.pump();

        // Should now show filled icon (enabled state)
        expect(find.byIcon(Icons.send), findsOneWidget);

        // Clear text
        await tester.enterText(find.byType(TextField), '');
        await tester.pump();

        // Should return to outlined icon (disabled state)
        expect(find.byIcon(Icons.send_outlined), findsOneWidget);
      });

      testWidgets('should handle multiline input', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChatInput(onSendMessage: onSendMessage),
            ),
          ),
        );

        final multilineText = 'Line 1\nLine 2\nLine 3';
        await tester.enterText(find.byType(TextField), multilineText);
        await tester.pump();

        expect(find.text(multilineText), findsOneWidget);
      });

      testWidgets('should respect character limit', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChatInput(onSendMessage: onSendMessage),
            ),
          ),
        );

        final textField = tester.widget<TextField>(find.byType(TextField));
        expect(textField.maxLength, 1000); // ChatConfig.maxMessageLength
      });
    });

    group('Message Sending', () {
      testWidgets('should send message when send button is tapped', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChatInput(onSendMessage: onSendMessage),
            ),
          ),
        );

        await tester.enterText(find.byType(TextField), 'Test message');
        await tester.pump();

        await tester.tap(find.byIcon(Icons.send));
        await tester.pump();

        expect(sentMessages.length, 1);
        expect(sentMessages.first, 'Test message');
      });

      testWidgets('should clear input after sending message', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChatInput(onSendMessage: onSendMessage),
            ),
          ),
        );

        await tester.enterText(find.byType(TextField), 'Test message');
        await tester.pump();

        await tester.tap(find.byIcon(Icons.send));
        await tester.pump();

        final textField = tester.widget<TextField>(find.byType(TextField));
        expect(textField.controller?.text, '');
      });

      testWidgets('should not send empty message', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChatInput(onSendMessage: onSendMessage),
            ),
          ),
        );

        await tester.tap(find.byIcon(Icons.send_outlined));
        await tester.pump();

        expect(sentMessages.length, 0);
      });

      testWidgets('should trim whitespace from message', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChatInput(onSendMessage: onSendMessage),
            ),
          ),
        );

        await tester.enterText(find.byType(TextField), '  Test message  ');
        await tester.pump();

        await tester.tap(find.byIcon(Icons.send));
        await tester.pump();

        expect(sentMessages.length, 1);
        expect(sentMessages.first, 'Test message');
      });

      testWidgets('should not send when disabled', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChatInput(
                onSendMessage: onSendMessage,
                isEnabled: false,
              ),
            ),
          ),
        );

        await tester.enterText(find.byType(TextField), 'Test message');
        await tester.pump();

        // TextField should be disabled, so text won't be entered
        final textField = tester.widget<TextField>(find.byType(TextField));
        expect(textField.enabled, false);
      });

      testWidgets('should not send when loading', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChatInput(
                onSendMessage: onSendMessage,
                isLoading: true,
              ),
            ),
          ),
        );

        // When loading, the TextField should still accept input but send should be disabled
        final textField = tester.widget<TextField>(find.byType(TextField));
        expect(textField.enabled, false); // Disabled when loading
      });
    });

    group('Keyboard Interactions', () {
      testWidgets('should send message on Enter when text is present on mobile', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChatInput(onSendMessage: onSendMessage),
            ),
          ),
        );

        await tester.enterText(find.byType(TextField), 'Test message');
        await tester.pump();

        await tester.testTextInput.receiveAction(TextInputAction.done);
        await tester.pump();

        expect(sentMessages.length, 1);
        expect(sentMessages.first, 'Test message');
      });

      // Note: Keyboard event testing for Ctrl/Cmd+Enter is limited in widget tests
      // due to platform-specific behavior and test environment limitations
    });

    group('Visual States', () {
      testWidgets('should show different visual states for send button', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChatInput(onSendMessage: onSendMessage),
            ),
          ),
        );

        // Initially should show outlined icon (disabled state)
        expect(find.byIcon(Icons.send_outlined), findsOneWidget);
        
        // Add text to enable send button
        await tester.enterText(find.byType(TextField), 'Test');
        await tester.pump();

        // Button should change to filled icon when text is present
        expect(find.byIcon(Icons.send), findsOneWidget);
        // Don't check for absence of outlined icon as both might be present during animation
      });

      testWidgets('should apply proper theming', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData.light(),
            home: Scaffold(
              body: ChatInput(onSendMessage: onSendMessage),
            ),
          ),
        );

        await tester.pump();
        expect(find.byType(ChatInput), findsOneWidget);

        // Test with dark theme
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData.dark(),
            home: Scaffold(
              body: ChatInput(onSendMessage: onSendMessage),
            ),
          ),
        );

        await tester.pump();
        expect(find.byType(ChatInput), findsOneWidget);
      });
    });

    group('Focus Management', () {
      testWidgets('should maintain focus after sending message', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChatInput(onSendMessage: onSendMessage),
            ),
          ),
        );

        // Tap to focus
        await tester.tap(find.byType(TextField));
        await tester.pump();

        await tester.enterText(find.byType(TextField), 'Test message');
        await tester.pump();

        await tester.tap(find.byIcon(Icons.send));
        await tester.pump();

        // Focus should be maintained (though testing focus in widget tests has limitations)
        expect(find.byType(TextField), findsOneWidget);
      });
    });

    group('Configuration Options', () {
      testWidgets('should respect maxLines configuration', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChatInput(
                onSendMessage: onSendMessage,
                maxLines: 3,
              ),
            ),
          ),
        );

        final textField = tester.widget<TextField>(find.byType(TextField));
        expect(textField.maxLines, 3);
      });

      testWidgets('should respect minLines configuration', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChatInput(
                onSendMessage: onSendMessage,
                minLines: 2,
              ),
            ),
          ),
        );

        final textField = tester.widget<TextField>(find.byType(TextField));
        expect(textField.minLines, 2);
      });
    });
  });

  group('ChatInputError', () {
    testWidgets('should display error message', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ChatInputError(message: 'Test error message'),
          ),
        ),
      );

      expect(find.text('Test error message'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });

    testWidgets('should show dismiss button when onDismiss provided', (tester) async {
      bool dismissed = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ChatInputError(
              message: 'Test error message',
              onDismiss: () => dismissed = true,
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.close), findsOneWidget);

      await tester.tap(find.byIcon(Icons.close));
      await tester.pump();

      expect(dismissed, true);
    });

    testWidgets('should not show dismiss button when onDismiss not provided', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ChatInputError(message: 'Test error message'),
          ),
        ),
      );

      expect(find.byIcon(Icons.close), findsNothing);
    });
  });

  group('ChatInputHint', () {
    testWidgets('should display hint message', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ChatInputHint(message: 'Test hint message'),
          ),
        ),
      );

      expect(find.text('Test hint message'), findsOneWidget);
    });

    testWidgets('should show icon when provided', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ChatInputHint(
              message: 'Test hint message',
              icon: Icons.info,
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.info), findsOneWidget);
    });

    testWidgets('should not show icon when not provided', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ChatInputHint(message: 'Test hint message'),
          ),
        ),
      );

      expect(find.byIcon(Icons.info), findsNothing);
    });
  });
}