import 'package:flutter_test/flutter_test.dart';
import 'package:ai_chat_app/features/chat/repositories/chat_repository.dart';
import 'package:ai_chat_app/features/chat/services/chat_api_service.dart';
import 'package:ai_chat_app/features/chat/models/api_models.dart';

class MockChatApiService extends ChatApiService {
  final Future<CompletionResponse> Function(String agentId)?
      _initializeSessionHandler;
  final Future<CompletionResponse> Function({
    required String agentId,
    required String message,
    required String sessionId,
  })? _sendMessageHandler;

  MockChatApiService({
    Future<CompletionResponse> Function(String agentId)?
        initializeSessionHandler,
    Future<CompletionResponse> Function({
      required String agentId,
      required String message,
      required String sessionId,
    })? sendMessageHandler,
  })  : _initializeSessionHandler = initializeSessionHandler,
        _sendMessageHandler = sendMessageHandler,
        super();

  @override
  Future<CompletionResponse> initializeSession(String agentId) async {
    if (_initializeSessionHandler != null) {
      return await _initializeSessionHandler!(agentId);
    }
    return CompletionResponse(
      code: 200,
      message: 'Success',
      data: CompletionData(
        answer: 'Welcome!',
        id: 'msg-123',
        sessionId: 'session-456',
        reference: {},
        param: [],
      ),
    );
  }

  @override
  Future<CompletionResponse> sendMessage({
    required String agentId,
    required String message,
    required String sessionId,
  }) async {
    if (_sendMessageHandler != null) {
      return await _sendMessageHandler!(
        agentId: agentId,
        message: message,
        sessionId: sessionId,
      );
    }
    return CompletionResponse(
      code: 200,
      message: 'Success',
      data: CompletionData(
        answer: 'Response to: $message',
        id: 'msg-789',
        sessionId: sessionId,
        reference: {},
        param: [],
      ),
    );
  }

  @override
  void dispose() {
    // Mock implementation - no actual resources to dispose
  }
}

void main() {
  group('ApiChatRepository', () {
    late ApiChatRepository repository;
    late MockChatApiService mockApiService;

    const testAgentId = 'test-agent-id';
    const testMessage = 'Hello, AI!';
    const testSessionId = 'session-123';

    setUp(() {
      mockApiService = MockChatApiService();
      repository = ApiChatRepository(apiService: mockApiService);
    });

    tearDown(() {
      repository.dispose();
    });

    group('initializeSession', () {
      test('should return successful response from API service', () async {
        final expectedResponse = CompletionResponse(
          code: 200,
          message: 'Success',
          data: CompletionData(
            answer: 'Welcome to the chat!',
            id: 'msg-welcome',
            sessionId: 'new-session-123',
            reference: {},
            param: [],
          ),
        );

        mockApiService = MockChatApiService(
          initializeSessionHandler: (agentId) async {
            expect(agentId, testAgentId);
            return expectedResponse;
          },
        );
        repository = ApiChatRepository(apiService: mockApiService);

        final result = await repository.initializeSession(testAgentId);

        expect(result.code, expectedResponse.code);
        expect(result.data.answer, expectedResponse.data.answer);
        expect(result.data.sessionId, expectedResponse.data.sessionId);
      });

      test('should not retry authentication errors', () async {
        int callCount = 0;
        mockApiService = MockChatApiService(
          initializeSessionHandler: (agentId) async {
            callCount++;
            throw AuthenticationException();
          },
        );
        repository = ApiChatRepository(apiService: mockApiService);

        expect(
          () => repository.initializeSession(testAgentId),
          throwsA(isA<AuthenticationException>()),
        );

        // Should only be called once (no retries)
        expect(callCount, 1);
      });

      test('should not retry invalid agent errors', () async {
        int callCount = 0;
        mockApiService = MockChatApiService(
          initializeSessionHandler: (agentId) async {
            callCount++;
            throw InvalidAgentException();
          },
        );
        repository = ApiChatRepository(apiService: mockApiService);

        expect(
          () => repository.initializeSession(testAgentId),
          throwsA(isA<InvalidAgentException>()),
        );

        // Should only be called once (no retries)
        expect(callCount, 1);
      });

      test('should retry network errors up to max retries', () async {
        int callCount = 0;
        mockApiService = MockChatApiService(
          initializeSessionHandler: (agentId) async {
            callCount++;
            throw NetworkException();
          },
        );
        repository = ApiChatRepository(apiService: mockApiService);

        try {
          await repository.initializeSession(testAgentId);
        } catch (e) {
          // Expected to throw after exhausting retries
          expect(e, isA<NetworkException>());
        }

        // Should be called 3 times (initial + 2 retries)
        expect(callCount, 3);
      });

      test('should retry timeout errors up to max retries', () async {
        int callCount = 0;
        mockApiService = MockChatApiService(
          initializeSessionHandler: (agentId) async {
            callCount++;
            throw TimeoutException();
          },
        );
        repository = ApiChatRepository(apiService: mockApiService);

        try {
          await repository.initializeSession(testAgentId);
        } catch (e) {
          // Expected to throw after exhausting retries
          expect(e, isA<TimeoutException>());
        }

        // Should be called 3 times (initial + 2 retries)
        expect(callCount, 3);
      });

      test('should retry server errors (5xx)', () async {
        int callCount = 0;
        mockApiService = MockChatApiService(
          initializeSessionHandler: (agentId) async {
            callCount++;
            throw ApiException('Server error', statusCode: 500);
          },
        );
        repository = ApiChatRepository(apiService: mockApiService);

        try {
          await repository.initializeSession(testAgentId);
        } catch (e) {
          // Expected to throw after exhausting retries
          expect(e, isA<ApiException>());
        }

        // Should be called 3 times (initial + 2 retries)
        expect(callCount, 3);
      });

      test('should retry rate limit errors (429)', () async {
        int callCount = 0;
        mockApiService = MockChatApiService(
          initializeSessionHandler: (agentId) async {
            callCount++;
            throw ApiException('Rate limit exceeded', statusCode: 429);
          },
        );
        repository = ApiChatRepository(apiService: mockApiService);

        try {
          await repository.initializeSession(testAgentId);
        } catch (e) {
          // Expected to throw after exhausting retries
          expect(e, isA<ApiException>());
        }

        // Should be called 3 times (initial + 2 retries)
        expect(callCount, 3);
      });

      test('should not retry client errors (4xx except 429)', () async {
        int callCount = 0;
        mockApiService = MockChatApiService(
          initializeSessionHandler: (agentId) async {
            callCount++;
            throw ApiException('Bad request', statusCode: 404);
          },
        );
        repository = ApiChatRepository(apiService: mockApiService);

        expect(
          () => repository.initializeSession(testAgentId),
          throwsA(isA<ApiException>()),
        );

        // Should only be called once (no retries for 4xx except 429)
        expect(callCount, 1);
      });

      test('should succeed after retry', () async {
        int callCount = 0;
        final expectedResponse = CompletionResponse(
          code: 200,
          message: 'Success',
          data: CompletionData(
            answer: 'Welcome after retry!',
            id: 'msg-retry',
            sessionId: 'retry-session',
            reference: {},
            param: [],
          ),
        );

        mockApiService = MockChatApiService(
          initializeSessionHandler: (agentId) async {
            callCount++;
            if (callCount < 2) {
              throw NetworkException();
            }
            return expectedResponse;
          },
        );
        repository = ApiChatRepository(apiService: mockApiService);

        final result = await repository.initializeSession(testAgentId);

        expect(result.code, expectedResponse.code);
        expect(result.data.answer, expectedResponse.data.answer);
        expect(callCount, 2); // Failed once, succeeded on retry
      });
    });

    group('sendMessage', () {
      test('should return successful response from API service', () async {
        final expectedResponse = CompletionResponse(
          code: 200,
          message: 'Success',
          data: CompletionData(
            answer: 'AI response to your message',
            id: 'msg-response',
            sessionId: testSessionId,
            reference: {},
            param: [],
          ),
        );

        mockApiService = MockChatApiService(
          sendMessageHandler: ({
            required String agentId,
            required String message,
            required String sessionId,
          }) async {
            expect(agentId, testAgentId);
            expect(message, testMessage);
            expect(sessionId, testSessionId);
            return expectedResponse;
          },
        );
        repository = ApiChatRepository(apiService: mockApiService);

        final result = await repository.sendMessage(
          agentId: testAgentId,
          message: testMessage,
          sessionId: testSessionId,
        );

        expect(result.code, expectedResponse.code);
        expect(result.data.answer, expectedResponse.data.answer);
        expect(result.data.sessionId, testSessionId);
      });

      test('should apply retry logic for sendMessage', () async {
        int callCount = 0;
        final expectedResponse = CompletionResponse(
          code: 200,
          message: 'Success',
          data: CompletionData(
            answer: 'Success after retry',
            id: 'msg-retry-success',
            sessionId: testSessionId,
            reference: {},
            param: [],
          ),
        );

        mockApiService = MockChatApiService(
          sendMessageHandler: ({
            required String agentId,
            required String message,
            required String sessionId,
          }) async {
            callCount++;
            if (callCount < 2) {
              throw TimeoutException();
            }
            return expectedResponse;
          },
        );
        repository = ApiChatRepository(apiService: mockApiService);

        final result = await repository.sendMessage(
          agentId: testAgentId,
          message: testMessage,
          sessionId: testSessionId,
        );

        expect(result.data.answer, expectedResponse.data.answer);
        expect(callCount, 2); // Failed once, succeeded on retry
      });
    });

    group('endSession', () {
      test('should complete without error', () async {
        expect(() => repository.endSession(testSessionId), returnsNormally);
      });
    });

    group('dispose', () {
      test('should dispose API service', () {
        expect(() => repository.dispose(), returnsNormally);
      });
    });
  });
}
