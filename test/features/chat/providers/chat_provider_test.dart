import 'package:flutter_test/flutter_test.dart';
import 'package:ai_chat_app/features/chat/providers/chat_provider.dart';
import 'package:ai_chat_app/features/chat/repositories/chat_repository.dart';
import 'package:ai_chat_app/features/chat/models/api_models.dart';
import 'package:ai_chat_app/features/chat/models/message.dart';
import 'package:ai_chat_app/features/chat/widgets/error_message.dart';

class MockChatRepository implements ChatRepository {
  final Future<CompletionResponse> Function(String agentId)?
      _initializeSessionHandler;
  final Future<CompletionResponse> Function({
    required String agentId,
    required String message,
    required String sessionId,
  })? _sendMessageHandler;
  final Future<void> Function(String sessionId)? _endSessionHandler;

  MockChatRepository({
    Future<CompletionResponse> Function(String agentId)?
        initializeSessionHandler,
    Future<CompletionResponse> Function({
      required String agentId,
      required String message,
      required String sessionId,
    })? sendMessageHandler,
    Future<void> Function(String sessionId)? endSessionHandler,
  })  : _initializeSessionHandler = initializeSessionHandler,
        _sendMessageHandler = sendMessageHandler,
        _endSessionHandler = endSessionHandler;

  @override
  Future<CompletionResponse> initializeSession(String agentId) async {
    if (_initializeSessionHandler != null) {
      return await _initializeSessionHandler!(agentId);
    }
    return CompletionResponse(
      code: 200,
      message: 'Success',
      data: CompletionData(
        answer: 'Welcome to the chat!',
        id: 'msg-welcome',
        sessionId: 'session-123',
        reference: {},
        param: [],
      ),
    );
  }

  @override
  Future<CompletionResponse> sendMessage({
    required String agentId,
    required String message,
    required String sessionId,
  }) async {
    if (_sendMessageHandler != null) {
      return await _sendMessageHandler!(
        agentId: agentId,
        message: message,
        sessionId: sessionId,
      );
    }
    return CompletionResponse(
      code: 200,
      message: 'Success',
      data: CompletionData(
        answer: 'Response to: $message',
        id: 'msg-response',
        sessionId: sessionId,
        reference: {},
        param: [],
      ),
    );
  }

  @override
  Future<void> endSession(String sessionId) async {
    if (_endSessionHandler != null) {
      await _endSessionHandler!(sessionId);
    }
  }

  @override
  void dispose() {
    // Mock implementation - no resources to dispose
  }
}

void main() {
  group('ChatProvider', () {
    late ChatProvider provider;
    late MockChatRepository mockRepository;

    const testAgentId = 'test-agent';
    const testMessage = 'Hello, AI!';

    setUp(() {
      mockRepository = MockChatRepository();
      provider = ChatProvider(
        repository: mockRepository,
        agentId: testAgentId,
      );
    });

    tearDown(() {
      provider.dispose();
    });

    group('Initial State', () {
      test('should have correct initial state', () {
        expect(provider.messages, isEmpty);
        expect(provider.currentSession, isNull);
        expect(provider.isLoading, false);
        expect(provider.isSending, false);
        expect(provider.error, isNull);
        expect(provider.sessionId, isNull);
        expect(provider.agentId, testAgentId);
        expect(provider.hasSession, false);
        expect(provider.hasMessages, false);
        expect(provider.isInitialized, false);
      });
    });

    group('Session Initialization', () {
      test('should initialize session successfully', () async {
        final expectedResponse = CompletionResponse(
          code: 200,
          message: 'Success',
          data: CompletionData(
            answer: 'Welcome to our chat!',
            id: 'welcome-msg',
            sessionId: 'new-session-456',
            reference: {'key': 'value'},
            param: ['param1'],
          ),
        );

        mockRepository = MockChatRepository(
          initializeSessionHandler: (agentId) async {
            expect(agentId, testAgentId);
            return expectedResponse;
          },
        );
        provider = ChatProvider(
          repository: mockRepository,
          agentId: testAgentId,
        );

        expect(provider.isLoading, false);
        
        final future = provider.initializeSession();
        expect(provider.isLoading, true);
        
        await future;

        expect(provider.isLoading, false);
        expect(provider.sessionId, 'new-session-456');
        expect(provider.hasSession, true);
        expect(provider.isInitialized, true);
        expect(provider.currentSession?.sessionId, 'new-session-456');
        expect(provider.currentSession?.agentId, testAgentId);
        expect(provider.messages.length, 1);
        expect(provider.messages.first.content, 'Welcome to our chat!');
        expect(provider.messages.first.type, MessageType.assistant);
        expect(provider.error, isNull);
      });

      test('should handle initialization errors', () async {
        mockRepository = MockChatRepository(
          initializeSessionHandler: (agentId) async {
            throw Exception('Network error');
          },
        );
        provider = ChatProvider(
          repository: mockRepository,
          agentId: testAgentId,
        );

        await provider.initializeSession();

        expect(provider.isLoading, false);
        expect(provider.sessionId, isNull);
        expect(provider.hasSession, false);
        expect(provider.isInitialized, false);
        expect(provider.error, contains('Failed to initialize session'));
        expect(provider.messages, isEmpty);
      });

      test('should not initialize if already loading', () async {
        int callCount = 0;
        mockRepository = MockChatRepository(
          initializeSessionHandler: (agentId) async {
            callCount++;
            await Future.delayed(Duration(milliseconds: 100));
            return CompletionResponse(
              code: 200,
              message: 'Success',
              data: CompletionData(
                answer: 'Welcome!',
                id: 'msg',
                sessionId: 'session',
                reference: {},
                param: [],
              ),
            );
          },
        );
        provider = ChatProvider(
          repository: mockRepository,
          agentId: testAgentId,
        );

        // Start two initialization calls simultaneously
        final future1 = provider.initializeSession();
        final future2 = provider.initializeSession();

        await Future.wait([future1, future2]);

        expect(callCount, 1); // Should only be called once
      });
    });

    group('Message Sending', () {
      setUp(() async {
        // Initialize session first
        await provider.initializeSession();
      });

      test('should send message successfully', () async {
        final expectedResponse = CompletionResponse(
          code: 200,
          message: 'Success',
          data: CompletionData(
            answer: 'AI response to your message',
            id: 'response-msg',
            sessionId: provider.sessionId!,
            reference: {'source': 'test'},
            param: ['response'],
          ),
        );

        mockRepository = MockChatRepository(
          initializeSessionHandler: (agentId) async {
            return CompletionResponse(
              code: 200,
              message: 'Success',
              data: CompletionData(
                answer: 'Welcome!',
                id: 'welcome',
                sessionId: 'session-123',
                reference: {},
                param: [],
              ),
            );
          },
          sendMessageHandler: ({
            required String agentId,
            required String message,
            required String sessionId,
          }) async {
            expect(agentId, testAgentId);
            expect(message, testMessage);
            expect(sessionId, provider.sessionId);
            return expectedResponse;
          },
        );
        provider = ChatProvider(
          repository: mockRepository,
          agentId: testAgentId,
        );
        await provider.initializeSession();

        final initialMessageCount = provider.messages.length;
        expect(provider.isSending, false);

        final future = provider.sendMessage(testMessage);
        expect(provider.isSending, true);

        await future;

        expect(provider.isSending, false);
        expect(provider.messages.length, initialMessageCount + 2); // User + AI message
        
        final userMessage = provider.messages[provider.messages.length - 2];
        final aiMessage = provider.messages.last;

        expect(userMessage.content, testMessage);
        expect(userMessage.type, MessageType.user);
        expect(userMessage.status, MessageStatus.sent);

        expect(aiMessage.content, 'AI response to your message');
        expect(aiMessage.type, MessageType.assistant);
        expect(aiMessage.status, MessageStatus.sent);
        expect(provider.error, isNull);
      });

      test('should handle send message errors', () async {
        mockRepository = MockChatRepository(
          initializeSessionHandler: (agentId) async {
            return CompletionResponse(
              code: 200,
              message: 'Success',
              data: CompletionData(
                answer: 'Welcome!',
                id: 'welcome',
                sessionId: 'session-123',
                reference: {},
                param: [],
              ),
            );
          },
          sendMessageHandler: ({
            required String agentId,
            required String message,
            required String sessionId,
          }) async {
            throw Exception('API Error');
          },
        );
        provider = ChatProvider(
          repository: mockRepository,
          agentId: testAgentId,
        );
        await provider.initializeSession();

        final initialMessageCount = provider.messages.length;
        await provider.sendMessage(testMessage);

        expect(provider.isSending, false);
        expect(provider.messages.length, initialMessageCount + 1); // Only user message
        
        final userMessage = provider.messages.last;
        expect(userMessage.status, MessageStatus.failed);
        expect(provider.error, contains('Failed to send message'));
      });

      test('should not send empty messages', () async {
        final initialMessageCount = provider.messages.length;
        
        await provider.sendMessage('   '); // Whitespace only
        await provider.sendMessage(''); // Empty string

        expect(provider.messages.length, initialMessageCount);
        expect(provider.isSending, false);
      });

      test('should not send message without session', () async {
        provider = ChatProvider(
          repository: mockRepository,
          agentId: testAgentId,
        );
        // Don't initialize session

        await provider.sendMessage(testMessage);

        expect(provider.messages, isEmpty);
        expect(provider.isSending, false);
      });

      test('should not send message while already sending', () async {
        int callCount = 0;
        mockRepository = MockChatRepository(
          initializeSessionHandler: (agentId) async {
            return CompletionResponse(
              code: 200,
              message: 'Success',
              data: CompletionData(
                answer: 'Welcome!',
                id: 'welcome',
                sessionId: 'session-123',
                reference: {},
                param: [],
              ),
            );
          },
          sendMessageHandler: ({
            required String agentId,
            required String message,
            required String sessionId,
          }) async {
            callCount++;
            await Future.delayed(Duration(milliseconds: 100));
            return CompletionResponse(
              code: 200,
              message: 'Success',
              data: CompletionData(
                answer: 'Response',
                id: 'response',
                sessionId: sessionId,
                reference: {},
                param: [],
              ),
            );
          },
        );
        provider = ChatProvider(
          repository: mockRepository,
          agentId: testAgentId,
        );
        await provider.initializeSession();

        // Start two send operations simultaneously
        final future1 = provider.sendMessage('Message 1');
        final future2 = provider.sendMessage('Message 2');

        await Future.wait([future1, future2]);

        expect(callCount, 1); // Should only be called once
      });
    });

    group('Message Retry', () {
      late String failedMessageId;

      setUp(() async {
        // Initialize session and create a failed message
        await provider.initializeSession();
        
        mockRepository = MockChatRepository(
          initializeSessionHandler: (agentId) async {
            return CompletionResponse(
              code: 200,
              message: 'Success',
              data: CompletionData(
                answer: 'Welcome!',
                id: 'welcome',
                sessionId: 'session-123',
                reference: {},
                param: [],
              ),
            );
          },
          sendMessageHandler: ({
            required String agentId,
            required String message,
            required String sessionId,
          }) async {
            throw Exception('Network error');
          },
        );
        provider = ChatProvider(
          repository: mockRepository,
          agentId: testAgentId,
        );
        await provider.initializeSession();
        
        await provider.sendMessage('Failed message');
        failedMessageId = provider.messages.last.id;
      });

      test('should retry failed message successfully', () async {
        expect(provider.messages.last.status, MessageStatus.failed);

        // Update the existing mock to handle retry
        int sendCallCount = 0;
        mockRepository = MockChatRepository(
          initializeSessionHandler: (agentId) async {
            return CompletionResponse(
              code: 200,
              message: 'Success',
              data: CompletionData(
                answer: 'Welcome!',
                id: 'welcome',
                sessionId: 'session-123',
                reference: {},
                param: [],
              ),
            );
          },
          sendMessageHandler: ({
            required String agentId,
            required String message,
            required String sessionId,
          }) async {
            sendCallCount++;
            if (sendCallCount == 1) {
              throw Exception('Network error'); // First call fails
            }
            return CompletionResponse(
              code: 200,
              message: 'Success',
              data: CompletionData(
                answer: 'Retry successful!',
                id: 'retry-response',
                sessionId: sessionId,
                reference: {},
                param: [],
              ),
            );
          },
        );
        
        // Create new provider with updated mock
        provider = ChatProvider(
          repository: mockRepository,
          agentId: testAgentId,
        );
        await provider.initializeSession();
        
        // Send message that will fail
        await provider.sendMessage('Failed message');
        failedMessageId = provider.messages.last.id;
        expect(provider.messages.last.status, MessageStatus.failed);

        final initialCount = provider.messages.length;
        await provider.retryMessage(failedMessageId);

        expect(provider.messages.length, initialCount + 1); // Added AI response
        final aiMessage = provider.messages.last;
        expect(aiMessage.content, 'Retry successful!');
        expect(aiMessage.type, MessageType.assistant);
      });

      test('should throw error for non-existent message', () async {
        expect(
          () => provider.retryMessage('non-existent-id'),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('Session Management', () {
      test('should start new session', () async {
        // Mock repository to return different session IDs
        int initCallCount = 0;
        mockRepository = MockChatRepository(
          initializeSessionHandler: (agentId) async {
            initCallCount++;
            return CompletionResponse(
              code: 200,
              message: 'Success',
              data: CompletionData(
                answer: 'Welcome!',
                id: 'welcome-$initCallCount',
                sessionId: 'session-$initCallCount',
                reference: {},
                param: [],
              ),
            );
          },
          sendMessageHandler: ({
            required String agentId,
            required String message,
            required String sessionId,
          }) async {
            return CompletionResponse(
              code: 200,
              message: 'Success',
              data: CompletionData(
                answer: 'Response to: $message',
                id: 'response',
                sessionId: sessionId,
                reference: {},
                param: [],
              ),
            );
          },
        );
        
        provider = ChatProvider(
          repository: mockRepository,
          agentId: testAgentId,
        );

        // Initialize first session
        await provider.initializeSession();
        await provider.sendMessage('Test message');
        
        final firstSessionId = provider.sessionId;
        final firstMessageCount = provider.messages.length;

        expect(firstSessionId, 'session-1');
        expect(firstMessageCount, greaterThan(0));

        // Start new session
        await provider.startNewSession();

        expect(provider.sessionId, 'session-2');
        expect(provider.sessionId, isNot(equals(firstSessionId)));
        expect(provider.messages.length, 1); // Only welcome message
        expect(provider.error, isNull);
      });

      test('should clear session', () async {
        await provider.initializeSession();
        await provider.sendMessage('Test message');

        expect(provider.hasSession, true);
        expect(provider.hasMessages, true);

        await provider.clearSession();

        expect(provider.sessionId, isNull);
        expect(provider.currentSession, isNull);
        expect(provider.messages, isEmpty);
        expect(provider.hasSession, false);
        expect(provider.hasMessages, false);
        expect(provider.error, isNull);
      });
    });

    group('Helper Methods', () {
      setUp(() async {
        await provider.initializeSession();
        await provider.sendMessage('User message 1');
        await provider.sendMessage('User message 2');
      });

      test('should get messages by type', () {
        final userMessages = provider.getMessagesByType(MessageType.user);
        final assistantMessages = provider.getMessagesByType(MessageType.assistant);

        expect(userMessages.length, 2);
        expect(assistantMessages.length, 3); // Welcome + 2 responses
        
        for (final message in userMessages) {
          expect(message.type, MessageType.user);
        }
        
        for (final message in assistantMessages) {
          expect(message.type, MessageType.assistant);
        }
      });

      test('should get pending messages', () {
        final pendingMessages = provider.getPendingMessages();
        expect(pendingMessages, isEmpty); // All should be sent by now
      });

      test('should get failed messages', () {
        final failedMessages = provider.getFailedMessages();
        expect(failedMessages, isEmpty); // No failed messages in this setup
      });
    });

    group('State Notifications', () {
      test('should notify listeners on state changes', () async {
        int notificationCount = 0;
        provider.addListener(() {
          notificationCount++;
        });

        await provider.initializeSession();
        expect(notificationCount, greaterThan(0));

        final initialCount = notificationCount;
        await provider.sendMessage('Test message');
        expect(notificationCount, greaterThan(initialCount));
      });
    });
  });
}  g
roup('Error Handling', () {
    test('should set chatError when session initialization fails', () async {
      final repository = MockChatRepository(
        initializeSessionHandler: (agentId) async {
          throw Exception('Network connection failed');
        },
      );
      final provider = ChatProvider(repository: repository);

      await provider.initializeSession();

      expect(provider.chatError, isNotNull);
      expect(provider.chatError!.type, ChatErrorType.network);
      expect(provider.chatError!.message, 'Network connection failed');
      expect(provider.chatError!.isRetryable, true);
      expect(provider.error, contains('Failed to initialize session'));
    });

    test('should set chatError when message sending fails', () async {
      final repository = MockChatRepository(
        sendMessageHandler: ({required agentId, required message, required sessionId}) async {
          throw Exception('TimeoutException: Request timed out');
        },
      );
      final provider = ChatProvider(repository: repository);
      
      // Initialize session first
      await provider.initializeSession();
      
      await provider.sendMessage('Hello');

      expect(provider.chatError, isNotNull);
      expect(provider.chatError!.type, ChatErrorType.timeout);
      expect(provider.chatError!.message, 'Request timed out');
      expect(provider.chatError!.isRetryable, true);
      
      // Check that user message is marked as failed
      final failedMessages = provider.getFailedMessages();
      expect(failedMessages.length, 1);
      expect(failedMessages.first.content, 'Hello');
      expect(failedMessages.first.status, MessageStatus.failed);
    });

    test('should handle authentication errors correctly', () async {
      final repository = MockChatRepository(
        initializeSessionHandler: (agentId) async {
          throw Exception('401 Unauthorized: Invalid API key');
        },
      );
      final provider = ChatProvider(repository: repository);

      await provider.initializeSession();

      expect(provider.chatError, isNotNull);
      expect(provider.chatError!.type, ChatErrorType.authentication);
      expect(provider.chatError!.message, 'Authentication failed');
      expect(provider.chatError!.isRetryable, false);
    });

    test('should handle validation errors correctly', () async {
      final repository = MockChatRepository(
        sendMessageHandler: ({required agentId, required message, required sessionId}) async {
          throw Exception('400 Bad Request: Invalid agent ID');
        },
      );
      final provider = ChatProvider(repository: repository);
      
      await provider.initializeSession();
      await provider.sendMessage('Hello');

      expect(provider.chatError, isNotNull);
      expect(provider.chatError!.type, ChatErrorType.validation);
      expect(provider.chatError!.message, 'Invalid request');
      expect(provider.chatError!.isRetryable, false);
    });

    test('should handle server errors correctly', () async {
      final repository = MockChatRepository(
        sendMessageHandler: ({required agentId, required message, required sessionId}) async {
          throw Exception('500 Internal Server Error');
        },
      );
      final provider = ChatProvider(repository: repository);
      
      await provider.initializeSession();
      await provider.sendMessage('Hello');

      expect(provider.chatError, isNotNull);
      expect(provider.chatError!.type, ChatErrorType.server);
      expect(provider.chatError!.message, 'Server error');
      expect(provider.chatError!.isRetryable, true);
    });

    test('should clear chatError when clearError is called', () async {
      final repository = MockChatRepository(
        initializeSessionHandler: (agentId) async {
          throw Exception('Network error');
        },
      );
      final provider = ChatProvider(repository: repository);

      await provider.initializeSession();
      expect(provider.chatError, isNotNull);

      provider.clearError();
      expect(provider.chatError, isNull);
      expect(provider.error, isNull);
    });

    test('should clear chatError when session is cleared', () async {
      final repository = MockChatRepository(
        initializeSessionHandler: (agentId) async {
          throw Exception('Network error');
        },
      );
      final provider = ChatProvider(repository: repository);

      await provider.initializeSession();
      expect(provider.chatError, isNotNull);

      await provider.clearSession();
      expect(provider.chatError, isNull);
      expect(provider.error, isNull);
    });

    test('should retry failed message with proper error handling', () async {
      bool firstCall = true;
      final repository = MockChatRepository(
        sendMessageHandler: ({required agentId, required message, required sessionId}) async {
          if (firstCall) {
            firstCall = false;
            throw Exception('Network connection failed');
          }
          return CompletionResponse(
            code: 200,
            message: 'Success',
            data: CompletionData(
              answer: 'Retry successful!',
              id: 'msg-retry',
              sessionId: sessionId,
              reference: {},
              param: [],
            ),
          );
        },
      );
      final provider = ChatProvider(repository: repository);
      
      await provider.initializeSession();
      await provider.sendMessage('Hello');

      // First attempt should fail
      expect(provider.chatError, isNotNull);
      expect(provider.getFailedMessages().length, 1);
      
      final failedMessageId = provider.getFailedMessages().first.id;
      
      // Retry should succeed
      await provider.retryMessage(failedMessageId);
      
      expect(provider.chatError, isNull);
      expect(provider.getFailedMessages().length, 0);
      expect(provider.messages.length, 3); // welcome + user + ai response
      expect(provider.messages.last.content, 'Retry successful!');
    });

    test('should handle retry failure correctly', () async {
      final repository = MockChatRepository(
        sendMessageHandler: ({required agentId, required message, required sessionId}) async {
          throw Exception('Persistent network error');
        },
      );
      final provider = ChatProvider(repository: repository);
      
      await provider.initializeSession();
      await provider.sendMessage('Hello');

      final failedMessageId = provider.getFailedMessages().first.id;
      
      // Retry should also fail
      await provider.retryMessage(failedMessageId);
      
      expect(provider.chatError, isNotNull);
      expect(provider.getFailedMessages().length, 1);
      expect(provider.getFailedMessages().first.status, MessageStatus.failed);
    });

    test('should provide retry callback in chatError', () async {
      final repository = MockChatRepository(
        initializeSessionHandler: (agentId) async {
          throw Exception('Network error');
        },
      );
      final provider = ChatProvider(repository: repository);

      await provider.initializeSession();

      expect(provider.chatError, isNotNull);
      expect(provider.chatError!.onRetry, isNotNull);
      
      // Verify retry callback works (though it will fail again in this test)
      expect(() => provider.chatError!.onRetry!(), returnsNormally);
    });
  });

  group('Error Recovery Scenarios', () {
    test('should recover from network error on retry', () async {
      bool networkFixed = false;
      final repository = MockChatRepository(
        initializeSessionHandler: (agentId) async {
          if (!networkFixed) {
            networkFixed = true;
            throw Exception('Network connection failed');
          }
          return CompletionResponse(
            code: 200,
            message: 'Success',
            data: CompletionData(
              answer: 'Welcome back!',
              id: 'msg-welcome-retry',
              sessionId: 'session-retry-123',
              reference: {},
              param: [],
            ),
          );
        },
      );
      final provider = ChatProvider(repository: repository);

      // First attempt fails
      await provider.initializeSession();
      expect(provider.chatError, isNotNull);
      expect(provider.hasSession, false);

      // Retry succeeds
      await provider.initializeSession();
      expect(provider.chatError, isNull);
      expect(provider.hasSession, true);
      expect(provider.messages.length, 1);
      expect(provider.messages.first.content, 'Welcome back!');
    });

    test('should handle mixed success and failure scenarios', () async {
      int callCount = 0;
      final repository = MockChatRepository(
        sendMessageHandler: ({required agentId, required message, required sessionId}) async {
          callCount++;
          if (callCount == 2) { // Second message fails
            throw Exception('Temporary server error');
          }
          return CompletionResponse(
            code: 200,
            message: 'Success',
            data: CompletionData(
              answer: 'Response to: $message',
              id: 'msg-$callCount',
              sessionId: sessionId,
              reference: {},
              param: [],
            ),
          );
        },
      );
      final provider = ChatProvider(repository: repository);
      
      await provider.initializeSession();
      
      // First message succeeds
      await provider.sendMessage('Hello 1');
      expect(provider.chatError, isNull);
      expect(provider.messages.length, 3); // welcome + user + ai
      
      // Second message fails
      await provider.sendMessage('Hello 2');
      expect(provider.chatError, isNotNull);
      expect(provider.getFailedMessages().length, 1);
      expect(provider.messages.length, 4); // welcome + user + ai + failed user
      
      // Third message succeeds
      await provider.sendMessage('Hello 3');
      expect(provider.chatError, isNull);
      expect(provider.messages.length, 6); // previous + user + ai
    });
  });