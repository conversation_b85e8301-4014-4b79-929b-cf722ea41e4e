import 'dart:convert';
import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:ai_chat_app/features/chat/services/chat_api_service.dart';
import 'package:ai_chat_app/features/chat/models/api_models.dart';

class MockHttpClient extends http.BaseClient {
  final http.Response Function(http.BaseRequest request) _handler;

  MockHttpClient(this._handler);

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) async {
    final response = _handler(request);
    return http.StreamedResponse(
      Stream.fromIterable([response.bodyBytes]),
      response.statusCode,
      headers: response.headers,
    );
  }
}

void main() {
  group('ChatApiService', () {
    late ChatApiService apiService;
    late MockHttpClient mockClient;

    const testBaseUrl = 'https://test-api.example.com';
    const testApiKey = 'test-api-key';
    const testAgentId = 'test-agent-id';

    setUp(() {
      mockClient = MockHttpClient((request) => http.Response('', 200));
      apiService = ChatApiService(
        client: mockClient,
        baseUrl: testBaseUrl,
        apiKey: testApiKey,
      );
    });

    tearDown(() {
      apiService.dispose();
    });

    group('initializeSession', () {
      test('should make POST request with correct headers and body', () async {
        final expectedResponse = CompletionResponse(
          code: 200,
          message: 'Success',
          data: CompletionData(
            answer: 'Welcome!',
            id: 'msg-123',
            sessionId: 'session-456',
            reference: {},
            param: [],
          ),
        );

        mockClient = MockHttpClient((request) {
          expect(request.method, 'POST');
          expect(request.url.toString(), '$testBaseUrl/completions');
          expect(request.headers['Content-Type'], contains('application/json'));
          expect(request.headers['Authorization'], 'Bearer $testApiKey');
          expect(request.headers['Accept'], 'application/json');

          final body = json.decode((request as http.Request).body);
          expect(body['agent_id'], testAgentId);
          expect(body['stream'], false);
          expect(body['session_id'], null);

          return http.Response(
            json.encode(expectedResponse.toJson()),
            200,
          );
        });

        apiService = ChatApiService(
          client: mockClient,
          baseUrl: testBaseUrl,
          apiKey: testApiKey,
        );

        final result = await apiService.initializeSession(testAgentId);

        expect(result.code, expectedResponse.code);
        expect(result.message, expectedResponse.message);
        expect(result.data.answer, expectedResponse.data.answer);
      });

      test('should throw AuthenticationException on 401 status', () async {
        mockClient =
            MockHttpClient((request) => http.Response('Unauthorized', 401));
        apiService = ChatApiService(
          client: mockClient,
          baseUrl: testBaseUrl,
          apiKey: testApiKey,
        );

        expect(
          () => apiService.initializeSession(testAgentId),
          throwsA(isA<AuthenticationException>()),
        );
      });

      test('should throw InvalidAgentException on 400 status', () async {
        mockClient =
            MockHttpClient((request) => http.Response('Bad Request', 400));
        apiService = ChatApiService(
          client: mockClient,
          baseUrl: testBaseUrl,
          apiKey: testApiKey,
        );

        expect(
          () => apiService.initializeSession(testAgentId),
          throwsA(isA<InvalidAgentException>()),
        );
      });
    });

    group('sendMessage', () {
      test('should make POST request with message and session ID', () async {
        const testMessage = 'Hello, AI!';
        const testSessionId = 'session-123';

        final expectedResponse = CompletionResponse(
          code: 200,
          message: 'Success',
          data: CompletionData(
            answer: 'Hello! How can I help you?',
            id: 'msg-456',
            sessionId: testSessionId,
            reference: {},
            param: [],
          ),
        );

        mockClient = MockHttpClient((request) {
          final body = json.decode((request as http.Request).body);
          expect(body['agent_id'], testAgentId);
          expect(body['question'], testMessage);
          expect(body['session_id'], testSessionId);
          expect(body['stream'], false);

          return http.Response(
            json.encode(expectedResponse.toJson()),
            200,
          );
        });

        apiService = ChatApiService(
          client: mockClient,
          baseUrl: testBaseUrl,
          apiKey: testApiKey,
        );

        final result = await apiService.sendMessage(
          agentId: testAgentId,
          message: testMessage,
          sessionId: testSessionId,
        );

        expect(result.code, expectedResponse.code);
        expect(result.data.answer, expectedResponse.data.answer);
        expect(result.data.sessionId, testSessionId);
      });
    });

    group('error handling', () {
      test('should throw NetworkException on SocketException', () async {
        mockClient = MockHttpClient(
            (request) => throw const SocketException('No internet'));
        apiService = ChatApiService(
          client: mockClient,
          baseUrl: testBaseUrl,
          apiKey: testApiKey,
        );

        expect(
          () => apiService.initializeSession(testAgentId),
          throwsA(isA<NetworkException>()),
        );
      });

      test('should throw ApiException for server errors', () async {
        mockClient = MockHttpClient(
            (request) => http.Response('Internal Server Error', 500));
        apiService = ChatApiService(
          client: mockClient,
          baseUrl: testBaseUrl,
          apiKey: testApiKey,
        );

        expect(
          () => apiService.initializeSession(testAgentId),
          throwsA(isA<ApiException>()),
        );
      });

      test('should throw ApiException for invalid JSON response', () async {
        mockClient =
            MockHttpClient((request) => http.Response('invalid json', 200));
        apiService = ChatApiService(
          client: mockClient,
          baseUrl: testBaseUrl,
          apiKey: testApiKey,
        );

        expect(
          () => apiService.initializeSession(testAgentId),
          throwsA(isA<ApiException>()),
        );
      });

      test('should handle rate limiting (429)', () async {
        mockClient = MockHttpClient(
            (request) => http.Response('Rate limit exceeded', 429));
        apiService = ChatApiService(
          client: mockClient,
          baseUrl: testBaseUrl,
          apiKey: testApiKey,
        );

        expect(
          () => apiService.initializeSession(testAgentId),
          throwsA(isA<ApiException>()),
        );
      });

      test('should handle 404 not found', () async {
        mockClient =
            MockHttpClient((request) => http.Response('Not Found', 404));
        apiService = ChatApiService(
          client: mockClient,
          baseUrl: testBaseUrl,
          apiKey: testApiKey,
        );

        expect(
          () => apiService.initializeSession(testAgentId),
          throwsA(isA<ApiException>()),
        );
      });
    });

    group('response parsing', () {
      test('should parse successful response correctly', () async {
        final responseData = {
          'code': 200,
          'message': 'Success',
          'data': {
            'answer': 'Test response',
            'id': 'msg-789',
            'session_id': 'session-789',
            'reference': {'key': 'value'},
            'param': ['param1', 'param2'],
          },
        };

        mockClient = MockHttpClient((request) {
          return http.Response(json.encode(responseData), 200);
        });

        apiService = ChatApiService(
          client: mockClient,
          baseUrl: testBaseUrl,
          apiKey: testApiKey,
        );

        final result = await apiService.initializeSession(testAgentId);

        expect(result.code, 200);
        expect(result.message, 'Success');
        expect(result.data.answer, 'Test response');
        expect(result.data.id, 'msg-789');
        expect(result.data.sessionId, 'session-789');
        expect(result.data.reference['key'], 'value');
        expect(result.data.param, ['param1', 'param2']);
      });

      test('should handle empty reference and param fields', () async {
        final responseData = {
          'code': 200,
          'message': 'Success',
          'data': {
            'answer': 'Test response',
            'id': 'msg-789',
            'session_id': 'session-789',
            'reference': {},
            'param': [],
          },
        };

        mockClient = MockHttpClient((request) {
          return http.Response(json.encode(responseData), 200);
        });

        apiService = ChatApiService(
          client: mockClient,
          baseUrl: testBaseUrl,
          apiKey: testApiKey,
        );

        final result = await apiService.initializeSession(testAgentId);

        expect(result.data.reference, isEmpty);
        expect(result.data.param, isEmpty);
      });
    });

    group('dispose', () {
      test('should close HTTP client when disposed', () {
        // This test verifies that dispose calls client.close()
        // The actual behavior is tested by ensuring no exceptions are thrown
        expect(() => apiService.dispose(), returnsNormally);
      });
    });
  });
}
