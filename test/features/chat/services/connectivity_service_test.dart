import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:ai_chat_app/features/chat/services/connectivity_service.dart';

void main() {
  group('ConnectivityService', () {
    late ConnectivityService service;

    setUp(() {
      service = ConnectivityService();
    });

    tearDown(() {
      service.stopMonitoring();
    });

    test('should be a singleton', () {
      final service1 = ConnectivityService();
      final service2 = ConnectivityService();
      expect(identical(service1, service2), true);
    });

    test('should start with connected state', () {
      expect(service.isConnected, true);
    });

    test('should start and stop monitoring', () {
      service.startMonitoring();
      expect(service.isConnected, isA<bool>());

      service.stopMonitoring();
      // Should not throw any errors
    });

    test('should check connectivity', () async {
      final result = await service.checkConnectivity();
      expect(result, isA<bool>());
    });

    test('should notify listeners when connectivity changes', () async {
      bool notified = false;
      service.addListener(() {
        notified = true;
      });

      // Force a connectivity check that might change state
      await service.checkConnectivity();

      // Note: This test might not always trigger a change in real scenarios
      // but ensures the listener mechanism works
      service.removeListener(() {});
    });
  });

  group('ConnectivityProvider Widget', () {
    testWidgets('should provide ConnectivityService to children',
        (tester) async {
      ConnectivityService? providedService;

      await tester.pumpWidget(
        ConnectivityProvider(
          child: MaterialApp(
            home: Consumer<ConnectivityService>(
              builder: (context, service, child) {
                providedService = service;
                return Container();
              },
            ),
          ),
        ),
      );

      expect(providedService, isNotNull);
      expect(providedService, isA<ConnectivityService>());

      // Clean up timers
      providedService!.stopMonitoring();
    });
  });

  group('ConnectivityIndicator Widget', () {
    testWidgets('should show connected state', (tester) async {
      final service = ConnectivityService();

      await tester.pumpWidget(
        ChangeNotifierProvider<ConnectivityService>.value(
          value: service,
          child: MaterialApp(
            home: Scaffold(
              body: ConnectivityIndicator(showWhenConnected: true),
            ),
          ),
        ),
      );

      // Should show connected indicator when showWhenConnected is true
      expect(find.text('Connected'), findsOneWidget);
      expect(find.byIcon(Icons.wifi), findsOneWidget);
    });

    testWidgets('should hide when connected and showWhenConnected is false',
        (tester) async {
      final service = ConnectivityService();

      await tester.pumpWidget(
        ChangeNotifierProvider<ConnectivityService>.value(
          value: service,
          child: MaterialApp(
            home: Scaffold(
              body: ConnectivityIndicator(showWhenConnected: false),
            ),
          ),
        ),
      );

      // Should not show anything when connected and showWhenConnected is false
      expect(find.text('Connected'), findsNothing);
      expect(find.text('No internet connection'), findsNothing);
    });

    testWidgets('should show child widget when provided', (tester) async {
      final service = ConnectivityService();

      await tester.pumpWidget(
        ChangeNotifierProvider<ConnectivityService>.value(
          value: service,
          child: MaterialApp(
            home: Scaffold(
              body: ConnectivityIndicator(
                showWhenConnected: false,
                child: Text('Child Widget'),
              ),
            ),
          ),
        ),
      );

      expect(find.text('Child Widget'), findsOneWidget);
    });
  });

  group('Connectivity Error Scenarios', () {
    test('should handle network lookup failures gracefully', () async {
      final service = ConnectivityService();

      // This test verifies that the service handles network failures
      // without throwing exceptions
      expect(() async => await service.checkConnectivity(), returnsNormally);
    });

    test('should update state when connectivity changes', () async {
      final service = ConnectivityService();
      bool stateChanged = false;

      service.addListener(() {
        stateChanged = true;
      });

      // Simulate connectivity check
      await service.checkConnectivity();

      // Clean up
      service.removeListener(() {});
    });
  });

  group('Integration Tests', () {
    testWidgets('should integrate with chat screen properly', (tester) async {
      await tester.pumpWidget(
        ConnectivityProvider(
          child: MaterialApp(
            home: Scaffold(
              body: Column(
                children: [
                  ConnectivityIndicator(),
                  Expanded(
                    child: Consumer<ConnectivityService>(
                      builder: (context, connectivity, child) {
                        return Center(
                          child: Text(
                            connectivity.isConnected
                                ? 'Chat Available'
                                : 'Chat Unavailable',
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Should show chat available when connected
      expect(find.text('Chat Available'), findsOneWidget);
    });
  });
}
