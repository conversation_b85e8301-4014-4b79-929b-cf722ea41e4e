import 'package:flutter_test/flutter_test.dart';
import 'package:ai_chat_app/features/chat/models/chat_session.dart';
import 'package:ai_chat_app/features/chat/models/message.dart';

void main() {
  group('ChatSession Tests', () {
    test('should create ChatSession with required values', () {
      final session = ChatSession(
        sessionId: 'session-123',
        agentId: 'agent-456',
      );

      expect(session.sessionId, 'session-123');
      expect(session.agentId, 'agent-456');
      expect(session.createdAt, isA<DateTime>());
      expect(session.messages, isEmpty);
      expect(session.hasMessages, false);
      expect(session.messageCount, 0);
    });

    test('should create ChatSession with custom values', () {
      final customDate = DateTime(2024, 1, 1);
      final messages = [
        Message(content: 'Hello', type: MessageType.user),
        Message(content: 'Hi there!', type: MessageType.assistant),
      ];

      final session = ChatSession(
        sessionId: 'session-123',
        agentId: 'agent-456',
        createdAt: customDate,
        messages: messages,
      );

      expect(session.sessionId, 'session-123');
      expect(session.agentId, 'agent-456');
      expect(session.createdAt, customDate);
      expect(session.messages.length, 2);
      expect(session.hasMessages, true);
      expect(session.messageCount, 2);
    });

    test('should serialize ChatSession to JSON correctly', () {
      final messages = [
        Message(
          id: 'msg-1',
          content: 'Hello',
          type: MessageType.user,
          timestamp: DateTime(2024, 1, 1),
        ),
      ];

      final session = ChatSession(
        sessionId: 'session-123',
        agentId: 'agent-456',
        createdAt: DateTime(2024, 1, 1),
        messages: messages,
      );

      final json = session.toJson();

      expect(json['sessionId'], 'session-123');
      expect(json['agentId'], 'agent-456');
      expect(json['createdAt'], '2024-01-01T00:00:00.000');
      expect(json['messages'], isA<List>());
      expect(json['messages'].length, 1);
    });

    test('should deserialize ChatSession from JSON correctly', () {
      final json = {
        'sessionId': 'session-123',
        'agentId': 'agent-456',
        'createdAt': '2024-01-01T00:00:00.000',
        'messages': [
          {
            'id': 'msg-1',
            'content': 'Hello',
            'type': 'user',
            'timestamp': '2024-01-01T00:00:00.000',
            'status': 'sent',
            'metadata': null,
          }
        ],
      };

      final session = ChatSession.fromJson(json);

      expect(session.sessionId, 'session-123');
      expect(session.agentId, 'agent-456');
      expect(session.createdAt, DateTime(2024, 1, 1));
      expect(session.messages.length, 1);
      expect(session.messages.first.content, 'Hello');
    });

    test('should handle missing messages in fromJson', () {
      final json = {
        'sessionId': 'session-123',
        'agentId': 'agent-456',
        'createdAt': '2024-01-01T00:00:00.000',
      };

      final session = ChatSession.fromJson(json);

      expect(session.messages, isEmpty);
    });

    test('should create copy with modified fields', () {
      final original = ChatSession(
        sessionId: 'session-123',
        agentId: 'agent-456',
      );

      final copy = original.copyWith(
        sessionId: 'session-789',
        agentId: 'agent-999',
      );

      expect(copy.sessionId, 'session-789');
      expect(copy.agentId, 'agent-999');
      expect(copy.createdAt, original.createdAt);
      expect(copy.messages, original.messages);
    });

    test('should add message to session', () {
      final session = ChatSession(
        sessionId: 'session-123',
        agentId: 'agent-456',
      );

      final message = Message(content: 'Hello', type: MessageType.user);
      final updatedSession = session.addMessage(message);

      expect(updatedSession.messages.length, 1);
      expect(updatedSession.messages.first.content, 'Hello');
      expect(session.messages.length, 0); // original unchanged
    });

    test('should update message in session', () {
      final message = Message(
        id: 'msg-1',
        content: 'Original',
        type: MessageType.user,
      );

      final session = ChatSession(
        sessionId: 'session-123',
        agentId: 'agent-456',
        messages: [message],
      );

      final updatedMessage = message.copyWith(content: 'Updated');
      final updatedSession = session.updateMessage('msg-1', updatedMessage);

      expect(updatedSession.messages.length, 1);
      expect(updatedSession.messages.first.content, 'Updated');
      expect(session.messages.first.content, 'Original'); // original unchanged
    });

    test('should remove message from session', () {
      final message1 =
          Message(id: 'msg-1', content: 'First', type: MessageType.user);
      final message2 =
          Message(id: 'msg-2', content: 'Second', type: MessageType.user);

      final session = ChatSession(
        sessionId: 'session-123',
        agentId: 'agent-456',
        messages: [message1, message2],
      );

      final updatedSession = session.removeMessage('msg-1');

      expect(updatedSession.messages.length, 1);
      expect(updatedSession.messages.first.id, 'msg-2');
      expect(session.messages.length, 2); // original unchanged
    });

    test('should get message by ID', () {
      final message =
          Message(id: 'msg-1', content: 'Hello', type: MessageType.user);
      final session = ChatSession(
        sessionId: 'session-123',
        agentId: 'agent-456',
        messages: [message],
      );

      final foundMessage = session.getMessageById('msg-1');
      final notFoundMessage = session.getMessageById('msg-999');

      expect(foundMessage, isNotNull);
      expect(foundMessage!.content, 'Hello');
      expect(notFoundMessage, isNull);
    });

    test('should get messages by type', () {
      final userMessage =
          Message(content: 'User message', type: MessageType.user);
      final assistantMessage =
          Message(content: 'Assistant message', type: MessageType.assistant);
      final systemMessage =
          Message(content: 'System message', type: MessageType.system);

      final session = ChatSession(
        sessionId: 'session-123',
        agentId: 'agent-456',
        messages: [userMessage, assistantMessage, systemMessage],
      );

      final userMessages = session.getMessagesByType(MessageType.user);
      final assistantMessages =
          session.getMessagesByType(MessageType.assistant);
      final errorMessages = session.getMessagesByType(MessageType.error);

      expect(userMessages.length, 1);
      expect(userMessages.first.content, 'User message');
      expect(assistantMessages.length, 1);
      expect(assistantMessages.first.content, 'Assistant message');
      expect(errorMessages.length, 0);
    });

    test('should get pending messages', () {
      final sentMessage = Message(
        content: 'Sent message',
        type: MessageType.user,
        status: MessageStatus.sent,
      );
      final sendingMessage = Message(
        content: 'Sending message',
        type: MessageType.user,
        status: MessageStatus.sending,
      );
      final failedMessage = Message(
        content: 'Failed message',
        type: MessageType.user,
        status: MessageStatus.failed,
      );

      final session = ChatSession(
        sessionId: 'session-123',
        agentId: 'agent-456',
        messages: [sentMessage, sendingMessage, failedMessage],
      );

      final pendingMessages = session.getPendingMessages();

      expect(pendingMessages.length, 1);
      expect(pendingMessages.first.content, 'Sending message');
      expect(session.hasPendingMessages, true);
    });

    test('should handle empty pending messages', () {
      final session = ChatSession(
        sessionId: 'session-123',
        agentId: 'agent-456',
        messages: [
          Message(
              content: 'Sent',
              type: MessageType.user,
              status: MessageStatus.sent),
        ],
      );

      expect(session.getPendingMessages(), isEmpty);
      expect(session.hasPendingMessages, false);
    });

    test('should implement equality correctly', () {
      final date = DateTime(2024, 1, 1);

      final session1 = ChatSession(
        sessionId: 'same-session',
        agentId: 'same-agent',
        createdAt: date,
      );

      final session2 = ChatSession(
        sessionId: 'same-session',
        agentId: 'same-agent',
        createdAt: date,
      );

      final session3 = ChatSession(
        sessionId: 'different-session',
        agentId: 'same-agent',
        createdAt: date,
      );

      expect(session1, equals(session2));
      expect(session1, isNot(equals(session3)));
      expect(session1.hashCode, equals(session2.hashCode));
    });

    test('should have proper toString implementation', () {
      final session = ChatSession(
        sessionId: 'session-123',
        agentId: 'agent-456',
        messages: [
          Message(content: 'Hello', type: MessageType.user),
          Message(content: 'Hi', type: MessageType.assistant),
        ],
      );

      final stringRepresentation = session.toString();

      expect(stringRepresentation, contains('session-123'));
      expect(stringRepresentation, contains('agent-456'));
      expect(stringRepresentation, contains('messageCount: 2'));
    });
  });
}
