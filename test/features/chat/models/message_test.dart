import 'package:flutter_test/flutter_test.dart';
import 'package:ai_chat_app/features/chat/models/message.dart';

void main() {
  group('Message Model Tests', () {
    test('should create a Message with default values', () {
      final message = Message(
        content: 'Hello, world!',
        type: MessageType.user,
      );

      expect(message.content, 'Hello, world!');
      expect(message.type, MessageType.user);
      expect(message.status, MessageStatus.sent);
      expect(message.id, isNotEmpty);
      expect(message.timestamp, isA<DateTime>());
      expect(message.metadata, isNull);
    });

    test('should create a Message with custom values', () {
      final customTimestamp = DateTime(2024, 1, 1);
      final customMetadata = {'key': 'value'};

      final message = Message(
        id: 'custom-id',
        content: 'Custom message',
        type: MessageType.assistant,
        timestamp: customTimestamp,
        status: MessageStatus.sending,
        metadata: customMetadata,
      );

      expect(message.id, 'custom-id');
      expect(message.content, 'Custom message');
      expect(message.type, MessageType.assistant);
      expect(message.timestamp, customTimestamp);
      expect(message.status, MessageStatus.sending);
      expect(message.metadata, customMetadata);
    });

    test('should serialize Message to JSON correctly', () {
      final timestamp = DateTime(2024, 1, 1, 12, 0, 0);
      final metadata = {'sessionId': '123', 'retryCount': 1};

      final message = Message(
        id: 'test-id',
        content: 'Test message',
        type: MessageType.user,
        timestamp: timestamp,
        status: MessageStatus.sent,
        metadata: metadata,
      );

      final json = message.toJson();

      expect(json['id'], 'test-id');
      expect(json['content'], 'Test message');
      expect(json['type'], 'user');
      expect(json['timestamp'], '2024-01-01T12:00:00.000');
      expect(json['status'], 'sent');
      expect(json['metadata'], metadata);
    });

    test('should deserialize Message from JSON correctly', () {
      final json = {
        'id': 'test-id',
        'content': 'Test message',
        'type': 'assistant',
        'timestamp': '2024-01-01T12:00:00.000',
        'status': 'sending',
        'metadata': {'key': 'value'},
      };

      final message = Message.fromJson(json);

      expect(message.id, 'test-id');
      expect(message.content, 'Test message');
      expect(message.type, MessageType.assistant);
      expect(message.timestamp, DateTime(2024, 1, 1, 12, 0, 0));
      expect(message.status, MessageStatus.sending);
      expect(message.metadata, {'key': 'value'});
    });

    test('should handle invalid enum values gracefully in fromJson', () {
      final json = {
        'id': 'test-id',
        'content': 'Test message',
        'type': 'invalid_type',
        'timestamp': '2024-01-01T12:00:00.000',
        'status': 'invalid_status',
        'metadata': null,
      };

      final message = Message.fromJson(json);

      expect(message.type, MessageType.system); // fallback
      expect(message.status, MessageStatus.sent); // fallback
    });

    test('should create copy with modified fields', () {
      final original = Message(
        content: 'Original message',
        type: MessageType.user,
        status: MessageStatus.sending,
      );

      final copy = original.copyWith(
        content: 'Modified message',
        status: MessageStatus.sent,
      );

      expect(copy.id, original.id);
      expect(copy.content, 'Modified message');
      expect(copy.type, original.type);
      expect(copy.timestamp, original.timestamp);
      expect(copy.status, MessageStatus.sent);
    });

    test('should implement equality correctly', () {
      final timestamp = DateTime(2024, 1, 1);

      final message1 = Message(
        id: 'same-id',
        content: 'Same content',
        type: MessageType.user,
        timestamp: timestamp,
        status: MessageStatus.sent,
      );

      final message2 = Message(
        id: 'same-id',
        content: 'Same content',
        type: MessageType.user,
        timestamp: timestamp,
        status: MessageStatus.sent,
      );

      final message3 = Message(
        id: 'different-id',
        content: 'Same content',
        type: MessageType.user,
        timestamp: timestamp,
        status: MessageStatus.sent,
      );

      expect(message1, equals(message2));
      expect(message1, isNot(equals(message3)));
      expect(message1.hashCode, equals(message2.hashCode));
    });

    test('should have proper toString implementation', () {
      final message = Message(
        id: 'test-id',
        content: 'Test content',
        type: MessageType.user,
        status: MessageStatus.sent,
      );

      final stringRepresentation = message.toString();

      expect(stringRepresentation, contains('test-id'));
      expect(stringRepresentation, contains('Test content'));
      expect(stringRepresentation, contains('MessageType.user'));
      expect(stringRepresentation, contains('MessageStatus.sent'));
    });

    group('MessageType enum tests', () {
      test('should have all expected values', () {
        expect(MessageType.values.length, 4);
        expect(MessageType.values, contains(MessageType.user));
        expect(MessageType.values, contains(MessageType.assistant));
        expect(MessageType.values, contains(MessageType.system));
        expect(MessageType.values, contains(MessageType.error));
      });
    });

    group('MessageStatus enum tests', () {
      test('should have all expected values', () {
        expect(MessageStatus.values.length, 3);
        expect(MessageStatus.values, contains(MessageStatus.sending));
        expect(MessageStatus.values, contains(MessageStatus.sent));
        expect(MessageStatus.values, contains(MessageStatus.failed));
      });
    });
  });
}
