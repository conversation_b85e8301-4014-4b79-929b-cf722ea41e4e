import 'package:flutter_test/flutter_test.dart';
import 'package:ai_chat_app/features/chat/models/api_models.dart';

void main() {
  group('CompletionRequest Tests', () {
    test('should create CompletionRequest with default values', () {
      final request = CompletionRequest();

      expect(request.question, isNull);
      expect(request.stream, false);
      expect(request.sessionId, isNull);
    });

    test('should create CompletionRequest with custom values', () {
      final request = CompletionRequest(
        question: 'Hello, AI!',
        stream: true,
        sessionId: 'session-123',
      );

      expect(request.question, 'Hello, AI!');
      expect(request.stream, true);
      expect(request.sessionId, 'session-123');
    });

    test('should serialize CompletionRequest to JSON correctly', () {
      final request = CompletionRequest(
        question: 'Test question',
        stream: true,
        sessionId: 'test-session',
      );

      final json = request.toJson();

      expect(json['question'], 'Test question');
      expect(json['stream'], true);
      expect(json['session_id'], 'test-session');
    });

    test('should serialize CompletionRequest with null values correctly', () {
      final request = CompletionRequest(stream: false);

      final json = request.toJson();

      expect(json['stream'], false);
      expect(json.containsKey('question'), false);
      expect(json.containsKey('session_id'), false);
    });

    test('should deserialize CompletionRequest from JSON correctly', () {
      final json = {
        'question': 'Test question',
        'stream': true,
        'session_id': 'test-session',
      };

      final request = CompletionRequest.fromJson(json);

      expect(request.question, 'Test question');
      expect(request.stream, true);
      expect(request.sessionId, 'test-session');
    });

    test('should handle missing fields in fromJson gracefully', () {
      final json = <String, dynamic>{};

      final request = CompletionRequest.fromJson(json);

      expect(request.question, isNull);
      expect(request.stream, false);
      expect(request.sessionId, isNull);
    });

    test('should implement equality correctly', () {
      final request1 = CompletionRequest(
        question: 'Same question',
        stream: true,
        sessionId: 'same-session',
      );

      final request2 = CompletionRequest(
        question: 'Same question',
        stream: true,
        sessionId: 'same-session',
      );

      final request3 = CompletionRequest(
        question: 'Different question',
        stream: true,
        sessionId: 'same-session',
      );

      expect(request1, equals(request2));
      expect(request1, isNot(equals(request3)));
      expect(request1.hashCode, equals(request2.hashCode));
    });
  });

  group('CompletionData Tests', () {
    test('should create CompletionData with required values', () {
      final data = CompletionData(
        answer: 'Test answer',
        id: 'test-id',
        sessionId: 'session-123',
        reference: {'key': 'value'},
        param: ['param1', 'param2'],
      );

      expect(data.answer, 'Test answer');
      expect(data.id, 'test-id');
      expect(data.sessionId, 'session-123');
      expect(data.reference, {'key': 'value'});
      expect(data.param, ['param1', 'param2']);
    });

    test('should deserialize CompletionData from JSON correctly', () {
      final json = {
        'answer': 'AI response',
        'id': 'response-id',
        'session_id': 'session-456',
        'reference': {'source': 'document'},
        'param': [1, 2, 3],
      };

      final data = CompletionData.fromJson(json);

      expect(data.answer, 'AI response');
      expect(data.id, 'response-id');
      expect(data.sessionId, 'session-456');
      expect(data.reference, {'source': 'document'});
      expect(data.param, [1, 2, 3]);
    });

    test('should handle missing fields in fromJson with defaults', () {
      final json = <String, dynamic>{};

      final data = CompletionData.fromJson(json);

      expect(data.answer, '');
      expect(data.id, '');
      expect(data.sessionId, '');
      expect(data.reference, {});
      expect(data.param, []);
    });

    test('should serialize CompletionData to JSON correctly', () {
      final data = CompletionData(
        answer: 'Test answer',
        id: 'test-id',
        sessionId: 'session-123',
        reference: {'key': 'value'},
        param: ['param1'],
      );

      final json = data.toJson();

      expect(json['answer'], 'Test answer');
      expect(json['id'], 'test-id');
      expect(json['session_id'], 'session-123');
      expect(json['reference'], {'key': 'value'});
      expect(json['param'], ['param1']);
    });

    test('should implement equality correctly', () {
      final data1 = CompletionData(
        answer: 'Same answer',
        id: 'same-id',
        sessionId: 'same-session',
        reference: {},
        param: [],
      );

      final data2 = CompletionData(
        answer: 'Same answer',
        id: 'same-id',
        sessionId: 'same-session',
        reference: {'different': 'reference'},
        param: ['different', 'param'],
      );

      final data3 = CompletionData(
        answer: 'Different answer',
        id: 'same-id',
        sessionId: 'same-session',
        reference: {},
        param: [],
      );

      expect(
          data1, equals(data2)); // equality only checks answer, id, sessionId
      expect(data1, isNot(equals(data3)));
      expect(data1.hashCode, equals(data2.hashCode));
    });
  });

  group('CompletionResponse Tests', () {
    test('should create CompletionResponse with required values', () {
      final data = CompletionData(
        answer: 'Test answer',
        id: 'test-id',
        sessionId: 'session-123',
        reference: {},
        param: [],
      );

      final response = CompletionResponse(
        code: 200,
        message: 'Success',
        data: data,
      );

      expect(response.code, 200);
      expect(response.message, 'Success');
      expect(response.data, data);
      expect(response.isSuccess, true);
      expect(response.isError, false);
    });

    test('should deserialize CompletionResponse from JSON correctly', () {
      final json = {
        'code': 200,
        'message': 'Success',
        'data': {
          'answer': 'AI response',
          'id': 'response-id',
          'session_id': 'session-456',
          'reference': {},
          'param': [],
        },
      };

      final response = CompletionResponse.fromJson(json);

      expect(response.code, 200);
      expect(response.message, 'Success');
      expect(response.data.answer, 'AI response');
      expect(response.data.id, 'response-id');
      expect(response.data.sessionId, 'session-456');
    });

    test('should handle missing fields in fromJson with defaults', () {
      final json = <String, dynamic>{};

      final response = CompletionResponse.fromJson(json);

      expect(response.code, 0);
      expect(response.message, '');
      expect(response.data.answer, '');
      expect(response.data.id, '');
      expect(response.data.sessionId, '');
    });

    test('should serialize CompletionResponse to JSON correctly', () {
      final data = CompletionData(
        answer: 'Test answer',
        id: 'test-id',
        sessionId: 'session-123',
        reference: {},
        param: [],
      );

      final response = CompletionResponse(
        code: 200,
        message: 'Success',
        data: data,
      );

      final json = response.toJson();

      expect(json['code'], 200);
      expect(json['message'], 'Success');
      expect(json['data'], isA<Map<String, dynamic>>());
      expect(json['data']['answer'], 'Test answer');
    });

    test('should correctly identify success and error states', () {
      final successResponse = CompletionResponse(
        code: 200,
        message: 'Success',
        data: CompletionData(
          answer: '',
          id: '',
          sessionId: '',
          reference: {},
          param: [],
        ),
      );

      final errorResponse = CompletionResponse(
        code: 400,
        message: 'Bad Request',
        data: CompletionData(
          answer: '',
          id: '',
          sessionId: '',
          reference: {},
          param: [],
        ),
      );

      expect(successResponse.isSuccess, true);
      expect(successResponse.isError, false);
      expect(errorResponse.isSuccess, false);
      expect(errorResponse.isError, true);
    });

    test('should implement equality correctly', () {
      final data = CompletionData(
        answer: 'Same answer',
        id: 'same-id',
        sessionId: 'same-session',
        reference: {},
        param: [],
      );

      final response1 = CompletionResponse(
        code: 200,
        message: 'Success',
        data: data,
      );

      final response2 = CompletionResponse(
        code: 200,
        message: 'Success',
        data: data,
      );

      final response3 = CompletionResponse(
        code: 400,
        message: 'Error',
        data: data,
      );

      expect(response1, equals(response2));
      expect(response1, isNot(equals(response3)));
      expect(response1.hashCode, equals(response2.hashCode));
    });
  });
}
