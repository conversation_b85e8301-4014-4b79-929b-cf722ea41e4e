import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:ai_chat_app/features/chat/screens/chat_screen.dart';
import 'package:ai_chat_app/features/chat/providers/chat_provider.dart';
import 'package:ai_chat_app/features/chat/repositories/chat_repository.dart';
import 'package:ai_chat_app/features/chat/models/api_models.dart';
import 'package:ai_chat_app/features/chat/models/message.dart';

class MockChatRepository implements ChatRepository {
  final Future<CompletionResponse> Function(String agentId)?
      _initializeSessionHandler;
  final Future<CompletionResponse> Function({
    required String agentId,
    required String message,
    required String sessionId,
  })? _sendMessageHandler;
  final Future<void> Function(String sessionId)? _endSessionHandler;

  MockChatRepository({
    Future<CompletionResponse> Function(String agentId)?
        initializeSessionHandler,
    Future<CompletionResponse> Function({
      required String agentId,
      required String message,
      required String sessionId,
    })? sendMessageHandler,
    Future<void> Function(String sessionId)? endSessionHandler,
  })  : _initializeSessionHandler = initializeSessionHandler,
        _sendMessageHandler = sendMessageHandler,
        _endSessionHandler = endSessionHandler;

  @override
  Future<CompletionResponse> initializeSession(String agentId) async {
    if (_initializeSessionHandler != null) {
      return await _initializeSessionHandler!(agentId);
    }
    return CompletionResponse(
      code: 200,
      message: 'Success',
      data: CompletionData(
        answer: 'Welcome to the chat!',
        id: 'welcome-msg',
        sessionId: 'test-session-123',
        reference: {},
        param: [],
      ),
    );
  }

  @override
  Future<CompletionResponse> sendMessage({
    required String agentId,
    required String message,
    required String sessionId,
  }) async {
    if (_sendMessageHandler != null) {
      return await _sendMessageHandler!(
        agentId: agentId,
        message: message,
        sessionId: sessionId,
      );
    }
    return CompletionResponse(
      code: 200,
      message: 'Success',
      data: CompletionData(
        answer: 'Test response to: $message',
        id: 'response-msg',
        sessionId: sessionId,
        reference: {},
        param: [],
      ),
    );
  }

  @override
  Future<void> endSession(String sessionId) async {
    if (_endSessionHandler != null) {
      await _endSessionHandler!(sessionId);
    }
  }

  @override
  void dispose() {}
}

void main() {
  group('ChatScreen Widget Tests', () {
    late MockChatRepository mockRepository;
    late ChatProvider chatProvider;

    setUp(() {
      mockRepository = MockChatRepository();
      chatProvider = ChatProvider(
        repository: mockRepository,
        agentId: 'test-agent',
      );
    });

    tearDown(() {
      chatProvider.dispose();
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: ChangeNotifierProvider<ChatProvider>.value(
          value: chatProvider,
          child: const ChatScreen(),
        ),
      );
    }

    testWidgets('should display app bar with title', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      expect(find.text('AI Chat'), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);
    });

    testWidgets('should display loading indicator during initialization', (WidgetTester tester) async {
      // Create a repository that delays initialization
      mockRepository = MockChatRepository(
        initializeSessionHandler: (agentId) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return CompletionResponse(
            code: 200,
            message: 'Success',
            data: CompletionData(
              answer: 'Welcome!',
              id: 'welcome',
              sessionId: 'session-123',
              reference: {},
              param: [],
            ),
          );
        },
      );
      chatProvider = ChatProvider(
        repository: mockRepository,
        agentId: 'test-agent',
      );

      await tester.pumpWidget(createTestWidget());
      
      // Should show loading indicator initially
      expect(find.text('Initializing chat session...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should display welcome message after initialization', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      // Wait for initialization to complete
      await tester.pumpAndSettle();
      
      // Should display welcome message
      expect(find.text('Welcome to the chat!'), findsOneWidget);
    });

    testWidgets('should display chat input when session is initialized', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();
      
      // Should have chat input
      expect(find.byType(TextField), findsOneWidget);
      expect(find.text('Type your message...'), findsOneWidget);
    });

    testWidgets('should display error state when initialization fails', (WidgetTester tester) async {
      mockRepository = MockChatRepository(
        initializeSessionHandler: (agentId) async {
          throw Exception('Network error');
        },
      );
      chatProvider = ChatProvider(
        repository: mockRepository,
        agentId: 'test-agent',
      );

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();
      
      // Should display error state
      expect(find.text('Failed to initialize chat'), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });

    testWidgets('should display refresh button in app bar', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();
      
      expect(find.byIcon(Icons.refresh), findsOneWidget);
      expect(find.byTooltip('Start New Session'), findsOneWidget);
    });

    testWidgets('should display connection status when session is active', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();
      
      // Should show connected status
      expect(find.text('Connected'), findsOneWidget);
    });
  });
}
