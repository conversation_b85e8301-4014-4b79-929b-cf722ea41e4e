import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:ai_chat_app/features/chat/providers/chat_provider.dart';
import 'package:ai_chat_app/features/chat/repositories/chat_repository.dart';
import 'package:ai_chat_app/features/chat/widgets/error_message.dart';
import 'package:ai_chat_app/features/chat/services/chat_api_service.dart';
import 'package:ai_chat_app/features/chat/models/api_models.dart';
import 'package:ai_chat_app/features/chat/screens/chat_screen.dart';
import 'package:ai_chat_app/features/chat/services/connectivity_service.dart';

class MockChatRepository implements ChatRepository {
  final Exception? initializeException;
  final Exception? sendMessageException;

  MockChatRepository({
    this.initializeException,
    this.sendMessageException,
  });

  @override
  Future<CompletionResponse> initializeSession(String agentId) async {
    if (initializeException != null) {
      throw initializeException!;
    }
    return CompletionResponse(
      code: 200,
      message: 'Success',
      data: CompletionData(
        answer: 'Welcome!',
        id: 'welcome',
        sessionId: 'session-123',
        reference: {},
        param: [],
      ),
    );
  }

  @override
  Future<CompletionResponse> sendMessage({
    required String agentId,
    required String message,
    required String sessionId,
  }) async {
    if (sendMessageException != null) {
      throw sendMessageException!;
    }
    return CompletionResponse(
      code: 200,
      message: 'Success',
      data: CompletionData(
        answer: 'Response to: $message',
        id: 'response',
        sessionId: sessionId,
        reference: {},
        param: [],
      ),
    );
  }

  @override
  Future<void> endSession(String sessionId) async {}

  @override
  void dispose() {}
}

void main() {
  group('Error Handling Tests', () {
    testWidgets('should display network error correctly', (WidgetTester tester) async {
      final error = ChatError.fromException(NetworkException());
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ErrorMessage(error: error),
          ),
        ),
      );

      expect(find.text('Network connection failed'), findsOneWidget);
      expect(find.text('Please check your internet connection and try again.'), findsOneWidget);
      expect(find.byIcon(Icons.wifi_off), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('should display authentication error correctly', (WidgetTester tester) async {
      final error = ChatError.fromException(AuthenticationException());
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ErrorMessage(error: error),
          ),
        ),
      );

      expect(find.text('Authentication failed'), findsOneWidget);
      expect(find.text('Invalid API key or credentials. Please check your configuration.'), findsOneWidget);
      expect(find.byIcon(Icons.lock), findsOneWidget);
      expect(find.text('Retry'), findsNothing); // Authentication errors are not retryable
    });

    testWidgets('should display timeout error correctly', (WidgetTester tester) async {
      final error = ChatError.fromException(TimeoutException());
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ErrorMessage(error: error),
          ),
        ),
      );

      expect(find.text('Request timed out'), findsOneWidget);
      expect(find.text('The request took too long to complete. Please try again.'), findsOneWidget);
      expect(find.byIcon(Icons.timer_off), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('should display server error correctly', (WidgetTester tester) async {
      final error = ChatError.fromException(
        ApiException('Server error', statusCode: 500),
      );
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ErrorMessage(error: error),
          ),
        ),
      );

      expect(find.text('Server error'), findsOneWidget);
      expect(find.text('The server is experiencing issues. Please try again later.'), findsOneWidget);
      expect(find.byIcon(Icons.dns), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('should handle session initialization errors', (WidgetTester tester) async {
      final mockRepository = MockChatRepository(
        initializeException: NetworkException(),
      );
      final chatProvider = ChatProvider(
        repository: mockRepository,
        agentId: 'test-agent',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: ConnectivityProvider(
            child: ChangeNotifierProvider<ChatProvider>.value(
              value: chatProvider,
              child: const ChatScreen(),
            ),
          ),
        ),
      );

      // Wait for initialization to complete
      await tester.pumpAndSettle();

      // Should display network error
      expect(find.text('Network connection failed'), findsOneWidget);
      expect(find.byIcon(Icons.wifi_off), findsOneWidget);

      chatProvider.dispose();
    });

    testWidgets('should handle message sending errors', (WidgetTester tester) async {
      final mockRepository = MockChatRepository(
        sendMessageException: TimeoutException(),
      );
      final chatProvider = ChatProvider(
        repository: mockRepository,
        agentId: 'test-agent',
      );

      // Initialize session first
      await chatProvider.initializeSession();

      await tester.pumpWidget(
        MaterialApp(
          home: ConnectivityProvider(
            child: ChangeNotifierProvider<ChatProvider>.value(
              value: chatProvider,
              child: const ChatScreen(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find and tap the text input
      final textField = find.byType(TextField);
      expect(textField, findsOneWidget);
      
      await tester.enterText(textField, 'Test message');
      await tester.testTextInput.receiveAction(TextInputAction.send);
      await tester.pumpAndSettle();

      // Should display timeout error banner
      expect(find.text('Request timed out'), findsOneWidget);

      chatProvider.dispose();
    });

    testWidgets('should display connectivity indicator', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ConnectivityProvider(
            child: const Scaffold(
              body: ConnectivityIndicator(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should show connectivity status (assuming connected in test environment)
      expect(find.byIcon(Icons.wifi), findsOneWidget);
    });

    test('should create appropriate ChatError from different exceptions', () {
      // Network error
      final networkError = ChatError.fromException(NetworkException());
      expect(networkError.type, ChatErrorType.network);
      expect(networkError.isRetryable, true);
      expect(networkError.icon, Icons.wifi_off);

      // Authentication error
      final authError = ChatError.fromException(AuthenticationException());
      expect(authError.type, ChatErrorType.authentication);
      expect(authError.isRetryable, false);
      expect(authError.icon, Icons.lock);

      // Timeout error
      final timeoutError = ChatError.fromException(TimeoutException());
      expect(timeoutError.type, ChatErrorType.timeout);
      expect(timeoutError.isRetryable, true);
      expect(timeoutError.icon, Icons.timer_off);

      // Server error
      final serverError = ChatError.fromException(
        ApiException('Server error', statusCode: 500),
      );
      expect(serverError.type, ChatErrorType.server);
      expect(serverError.isRetryable, true);
      expect(serverError.icon, Icons.dns);

      // Validation error
      final validationError = ChatError.fromException(
        ApiException('Invalid agent', statusCode: 400),
      );
      expect(validationError.type, ChatErrorType.validation);
      expect(validationError.isRetryable, false);
      expect(validationError.icon, Icons.warning);

      // Unknown error
      final unknownError = ChatError.fromException(Exception('Unknown error'));
      expect(unknownError.type, ChatErrorType.unknown);
      expect(unknownError.isRetryable, true);
      expect(unknownError.icon, Icons.error);
    });
  });
}
