import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:ai_chat_app/features/chat/models/api_models.dart';

/// Mock API server for testing chat functionality
class MockApiServer {
  HttpServer? _server;
  int? _port;
  final Map<String, String> _sessions = {};
  final Map<String, List<Map<String, dynamic>>> _conversationHistory = {};
  final Random _random = Random();

  // Configuration
  bool simulateNetworkDelay = true;
  Duration networkDelay = const Duration(milliseconds: 100);
  bool simulateErrors = false;
  double errorRate = 0.1; // 10% error rate when enabled
  bool simulateTimeouts = false;
  Duration timeoutDuration = const Duration(seconds: 30);

  /// Start the mock server
  Future<void> start({int? port}) async {
    _port = port ?? await _findAvailablePort();
    _server = await HttpServer.bind('localhost', _port!);
    
    _server!.listen((HttpRequest request) async {
      await _handleRequest(request);
    });
    
    print('Mock API server started on http://localhost:$_port');
  }

  /// Stop the mock server
  Future<void> stop() async {
    await _server?.close();
    _server = null;
    _port = null;
    _sessions.clear();
    _conversationHistory.clear();
    print('Mock API server stopped');
  }

  /// Get the server URL
  String get baseUrl => 'http://localhost:$_port';

  /// Get the completions endpoint URL
  String get completionsEndpoint => '$baseUrl/completions';

  /// Handle incoming HTTP requests
  Future<void> _handleRequest(HttpRequest request) async {
    // Add CORS headers
    request.response.headers.add('Access-Control-Allow-Origin', '*');
    request.response.headers.add('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    request.response.headers.add('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (request.method == 'OPTIONS') {
      request.response.statusCode = 200;
      await request.response.close();
      return;
    }

    // Simulate network delay
    if (simulateNetworkDelay) {
      await Future.delayed(networkDelay);
    }

    // Simulate timeouts
    if (simulateTimeouts) {
      await Future.delayed(timeoutDuration);
      request.response.statusCode = 408; // Request Timeout
      await request.response.close();
      return;
    }

    // Simulate random errors
    if (simulateErrors && _random.nextDouble() < errorRate) {
      await _simulateRandomError(request);
      return;
    }

    try {
      if (request.uri.path == '/completions' && request.method == 'POST') {
        await _handleCompletionsRequest(request);
      } else {
        request.response.statusCode = 404;
        request.response.write(jsonEncode({
          'code': 404,
          'message': 'Endpoint not found',
        }));
      }
    } catch (e) {
      request.response.statusCode = 500;
      request.response.write(jsonEncode({
        'code': 500,
        'message': 'Internal server error: $e',
      }));
    }

    await request.response.close();
  }

  /// Handle completions API requests
  Future<void> _handleCompletionsRequest(HttpRequest request) async {
    final body = await utf8.decoder.bind(request).join();
    final requestData = jsonDecode(body) as Map<String, dynamic>;

    // Validate required fields
    if (!requestData.containsKey('agent_id')) {
      request.response.statusCode = 400;
      request.response.write(jsonEncode({
        'code': 400,
        'message': 'Missing required field: agent_id',
      }));
      return;
    }

    final agentId = requestData['agent_id'] as String;
    final question = requestData['question'] as String?;
    final sessionId = requestData['session_id'] as String?;

    // Handle session initialization
    if (sessionId == null || sessionId.isEmpty) {
      await _handleSessionInitialization(request, agentId);
      return;
    }

    // Handle message sending
    if (question != null && question.isNotEmpty) {
      await _handleMessageSending(request, agentId, question, sessionId);
      return;
    }

    // Invalid request
    request.response.statusCode = 400;
    request.response.write(jsonEncode({
      'code': 400,
      'message': 'Invalid request: missing question for existing session',
    }));
  }

  /// Handle session initialization
  Future<void> _handleSessionInitialization(HttpRequest request, String agentId) async {
    final newSessionId = _generateSessionId();
    _sessions[newSessionId] = agentId;
    _conversationHistory[newSessionId] = [];

    final response = CompletionResponse(
      code: 200,
      message: 'Success',
      data: CompletionData(
        answer: 'Welcome! I\'m your AI assistant. How can I help you today?',
        id: _generateMessageId(),
        sessionId: newSessionId,
        reference: {
          'agent_id': agentId,
          'session_created': DateTime.now().toIso8601String(),
          'capabilities': ['text_generation', 'question_answering', 'conversation'],
        },
        param: ['session_initialized', 'welcome_message_sent'],
      ),
    );

    request.response.statusCode = 200;
    request.response.headers.contentType = ContentType.json;
    request.response.write(jsonEncode(response.toJson()));
  }

  /// Handle message sending
  Future<void> _handleMessageSending(
    HttpRequest request,
    String agentId,
    String question,
    String sessionId,
  ) async {
    // Validate session exists
    if (!_sessions.containsKey(sessionId)) {
      request.response.statusCode = 400;
      request.response.write(jsonEncode({
        'code': 400,
        'message': 'Invalid session ID',
      }));
      return;
    }

    // Add user message to history
    _conversationHistory[sessionId]!.add({
      'role': 'user',
      'content': question,
      'timestamp': DateTime.now().toIso8601String(),
    });

    // Generate AI response based on question
    final aiResponse = _generateAiResponse(question, sessionId);
    
    // Add AI response to history
    _conversationHistory[sessionId]!.add({
      'role': 'assistant',
      'content': aiResponse['answer'],
      'timestamp': DateTime.now().toIso8601String(),
    });

    final response = CompletionResponse(
      code: 200,
      message: 'Success',
      data: CompletionData(
        answer: aiResponse['answer'] as String,
        id: _generateMessageId(),
        sessionId: sessionId,
        reference: aiResponse['reference'] as Map<String, dynamic>?,
        param: aiResponse['param'] as List?,
      ),
    );

    request.response.statusCode = 200;
    request.response.headers.contentType = ContentType.json;
    request.response.write(jsonEncode(response.toJson()));
  }

  /// Generate AI response based on user question
  Map<String, dynamic> _generateAiResponse(String question, String sessionId) {
    final lowerQuestion = question.toLowerCase();
    
    // Handle specific test scenarios
    if (lowerQuestion.contains('table') || lowerQuestion.contains('data')) {
      return {
        'answer': 'Here is the requested data in table format:',
        'reference': {
          'data_source': 'mock_database',
          'query_time': DateTime.now().toIso8601String(),
        },
        'param': ['table_data_requested'],
        'table': [
          {'name': 'Alice', 'age': 30, 'city': 'New York'},
          {'name': 'Bob', 'age': 25, 'city': 'Los Angeles'},
          {'name': 'Charlie', 'age': 35, 'city': 'Chicago'},
        ],
      };
    }

    if (lowerQuestion.contains('error') || lowerQuestion.contains('fail')) {
      return {
        'answer': 'I encountered an issue processing your request. Please try again.',
        'reference': {
          'error_type': 'processing_error',
          'timestamp': DateTime.now().toIso8601String(),
        },
        'param': ['error_simulation'],
      };
    }

    if (lowerQuestion.contains('help') || lowerQuestion.contains('what can you do')) {
      return {
        'answer': 'I can help you with various tasks including answering questions, providing information, and having conversations. What would you like to know?',
        'reference': {
          'capabilities': ['question_answering', 'information_retrieval', 'conversation'],
          'version': '1.0.0',
        },
        'param': ['help_requested', 'capabilities_listed'],
      };
    }

    // Default response
    final conversationLength = _conversationHistory[sessionId]?.length ?? 0;
    return {
      'answer': 'Thank you for your message: "$question". This is response #${conversationLength ~/ 2 + 1} in our conversation.',
      'reference': {
        'conversation_turn': conversationLength ~/ 2 + 1,
        'session_id': sessionId,
        'response_generated': DateTime.now().toIso8601String(),
      },
      'param': ['standard_response', 'conversation_continued'],
    };
  }

  /// Simulate random errors for testing
  Future<void> _simulateRandomError(HttpRequest request) async {
    final errorTypes = [400, 401, 403, 429, 500, 502, 503];
    final statusCode = errorTypes[_random.nextInt(errorTypes.length)];
    
    final errorMessages = {
      400: 'Bad Request - Invalid input',
      401: 'Unauthorized - Invalid API key',
      403: 'Forbidden - Access denied',
      429: 'Too Many Requests - Rate limit exceeded',
      500: 'Internal Server Error',
      502: 'Bad Gateway',
      503: 'Service Unavailable',
    };

    request.response.statusCode = statusCode;
    request.response.write(jsonEncode({
      'code': statusCode,
      'message': errorMessages[statusCode] ?? 'Unknown error',
    }));
  }

  /// Generate a unique session ID
  String _generateSessionId() {
    return 'session_${DateTime.now().millisecondsSinceEpoch}_${_random.nextInt(10000)}';
  }

  /// Generate a unique message ID
  String _generateMessageId() {
    return 'msg_${DateTime.now().millisecondsSinceEpoch}_${_random.nextInt(10000)}';
  }

  /// Find an available port for the server
  Future<int> _findAvailablePort() async {
    for (int port = 8080; port <= 8090; port++) {
      try {
        final server = await HttpServer.bind('localhost', port);
        await server.close();
        return port;
      } catch (e) {
        // Port is not available, try next one
      }
    }
    throw Exception('No available ports found');
  }

  /// Get conversation history for a session
  List<Map<String, dynamic>>? getConversationHistory(String sessionId) {
    return _conversationHistory[sessionId];
  }

  /// Get all active sessions
  Map<String, String> get activeSessions => Map.from(_sessions);

  /// Reset server state
  void reset() {
    _sessions.clear();
    _conversationHistory.clear();
    simulateErrors = false;
    simulateTimeouts = false;
    simulateNetworkDelay = true;
    networkDelay = const Duration(milliseconds: 100);
    errorRate = 0.1;
  }
}
