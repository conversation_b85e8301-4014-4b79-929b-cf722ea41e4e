import 'package:flutter_test/flutter_test.dart';
import 'package:ai_chat_app/security/input_validator.dart';
import 'package:ai_chat_app/security/security_service.dart';

void main() {
  group('Input Validator Tests', () {
    group('API Key Validation', () {
      test('should accept valid API key', () {
        final result = InputValidator.validateApiKey('valid-api-key-123456');
        expect(result.isValid, isTrue);
        expect(result.sanitizedValue, equals('valid-api-key-123456'));
      });

      test('should accept Bearer token format', () {
        final result = InputValidator.validateApiKey('Bearer abc123def456ghi789');
        expect(result.isValid, isTrue);
        expect(result.sanitizedValue, equals('Bearer abc123def456ghi789'));
      });

      test('should reject empty API key', () {
        final result = InputValidator.validateApiKey('');
        expect(result.isValid, isFalse);
        expect(result.error, contains('API key is required'));
      });

      test('should reject short API key', () {
        final result = InputValidator.validateApiKey('short');
        expect(result.isValid, isFalse);
        expect(result.error, contains('at least 10 characters'));
      });

      test('should reject API key with invalid characters', () {
        final result = InputValidator.validateApiKey('invalid@key#with$symbols');
        expect(result.isValid, isFalse);
        expect(result.error, contains('invalid characters'));
      });

      test('should reject test/demo keys', () {
        final result = InputValidator.validateApiKey('test-api-key-123');
        expect(result.isValid, isFalse);
        expect(result.error, contains('Test or demo API keys'));
      });

      test('should reject overly long API key', () {
        final longKey = 'a' * 201;
        final result = InputValidator.validateApiKey(longKey);
        expect(result.isValid, isFalse);
        expect(result.error, contains('too long'));
      });
    });

    group('Agent ID Validation', () {
      test('should accept valid agent ID', () {
        final result = InputValidator.validateAgentId('valid-agent-123');
        expect(result.isValid, isTrue);
        expect(result.sanitizedValue, equals('valid-agent-123'));
      });

      test('should reject empty agent ID', () {
        final result = InputValidator.validateAgentId('');
        expect(result.isValid, isFalse);
        expect(result.error, contains('Agent ID is required'));
      });

      test('should reject short agent ID', () {
        final result = InputValidator.validateAgentId('ab');
        expect(result.isValid, isFalse);
        expect(result.error, contains('at least 3 characters'));
      });

      test('should reject agent ID with invalid characters', () {
        final result = InputValidator.validateAgentId('invalid@agent#id');
        expect(result.isValid, isFalse);
        expect(result.error, contains('invalid characters'));
      });

      test('should reject reserved words', () {
        final result = InputValidator.validateAgentId('admin');
        expect(result.isValid, isFalse);
        expect(result.error, contains('reserved word'));
      });

      test('should reject overly long agent ID', () {
        final longId = 'a' * 51;
        final result = InputValidator.validateAgentId(longId);
        expect(result.isValid, isFalse);
        expect(result.error, contains('too long'));
      });
    });

    group('Message Validation', () {
      test('should accept valid message', () {
        final result = InputValidator.validateMessage('Hello, how are you?');
        expect(result.isValid, isTrue);
        expect(result.sanitizedValue, equals('Hello, how are you?'));
      });

      test('should reject empty message', () {
        final result = InputValidator.validateMessage('');
        expect(result.isValid, isFalse);
        expect(result.error, contains('cannot be empty'));
      });

      test('should reject overly long message', () {
        final longMessage = 'a' * 10001;
        final result = InputValidator.validateMessage(longMessage);
        expect(result.isValid, isFalse);
        expect(result.error, contains('too long'));
      });

      test('should reject message with script tags', () {
        final result = InputValidator.validateMessage('Hello <script>alert("xss")</script>');
        expect(result.isValid, isFalse);
        expect(result.error, contains('dangerous content'));
      });

      test('should reject message with SQL injection patterns', () {
        final result = InputValidator.validateMessage('Hello; DROP TABLE users;');
        expect(result.isValid, isFalse);
        expect(result.error, contains('malicious SQL patterns'));
      });

      test('should sanitize message content', () {
        final result = InputValidator.validateMessage('Hello\x00world\t\n  ');
        expect(result.isValid, isTrue);
        expect(result.sanitizedValue, equals('Helloworld\t\n'));
      });

      test('should reject javascript: URLs', () {
        final result = InputValidator.validateMessage('Click here: javascript:alert("xss")');
        expect(result.isValid, isFalse);
        expect(result.error, contains('dangerous content'));
      });
    });

    group('URL Validation', () {
      test('should accept valid HTTPS URL', () {
        final result = InputValidator.validateUrl('https://api.example.com');
        expect(result.isValid, isTrue);
        expect(result.sanitizedValue, equals('https://api.example.com'));
      });

      test('should accept valid HTTP URL', () {
        final result = InputValidator.validateUrl('http://api.example.com');
        expect(result.isValid, isTrue);
        expect(result.sanitizedValue, equals('http://api.example.com'));
      });

      test('should reject empty URL', () {
        final result = InputValidator.validateUrl('');
        expect(result.isValid, isFalse);
        expect(result.error, contains('URL is required'));
      });

      test('should reject invalid URL format', () {
        final result = InputValidator.validateUrl('not-a-url');
        expect(result.isValid, isFalse);
        expect(result.error, contains('Invalid URL format'));
      });

      test('should reject URL without scheme', () {
        final result = InputValidator.validateUrl('api.example.com');
        expect(result.isValid, isFalse);
        expect(result.error, contains('must include a scheme'));
      });

      test('should reject localhost URLs', () {
        final result = InputValidator.validateUrl('http://localhost:8080');
        expect(result.isValid, isFalse);
        expect(result.error, contains('Local and private network URLs'));
      });

      test('should reject private IP URLs', () {
        final result = InputValidator.validateUrl('http://***********');
        expect(result.isValid, isFalse);
        expect(result.error, contains('Local and private network URLs'));
      });
    });

    group('JSON Structure Validation', () {
      test('should accept valid JSON structure', () {
        final json = {
          'agent_id': 'valid-agent-123',
          'question': 'Hello world',
          'session_id': 'session-123',
        };
        final result = InputValidator.validateJsonStructure(json);
        expect(result.isValid, isTrue);
      });

      test('should reject JSON without agent_id', () {
        final json = {'question': 'Hello world'};
        final result = InputValidator.validateJsonStructure(json);
        expect(result.isValid, isFalse);
        expect(result.error, contains('Missing required field: agent_id'));
      });

      test('should reject JSON with invalid agent_id', () {
        final json = {
          'agent_id': 'ab', // too short
          'question': 'Hello world',
        };
        final result = InputValidator.validateJsonStructure(json);
        expect(result.isValid, isFalse);
        expect(result.error, contains('Invalid agent_id'));
      });

      test('should reject JSON with invalid question', () {
        final json = {
          'agent_id': 'valid-agent-123',
          'question': '<script>alert("xss")</script>',
        };
        final result = InputValidator.validateJsonStructure(json);
        expect(result.isValid, isFalse);
        expect(result.error, contains('Invalid question'));
      });
    });

    group('Rate Limiting', () {
      test('should allow requests within rate limit', () {
        final result = InputValidator.validateRateLimit('test-user-1');
        expect(result.isValid, isTrue);
      });

      test('should reject requests exceeding rate limit', () {
        // Make many requests to exceed rate limit
        for (int i = 0; i < 61; i++) {
          InputValidator.validateRateLimit('test-user-2');
        }
        
        final result = InputValidator.validateRateLimit('test-user-2');
        expect(result.isValid, isFalse);
        expect(result.error, contains('Rate limit exceeded'));
      });
    });
  });

  group('Security Service Tests', () {
    test('should validate and sanitize request parameters', () {
      final result = SecurityService.validateAndSanitizeRequest(
        agentId: 'valid-agent-123',
        message: 'Hello world',
        sessionId: 'session-123',
      );

      expect(result['agent_id'], equals('valid-agent-123'));
      expect(result['question'], equals('Hello world'));
      expect(result['session_id'], equals('session-123'));
      expect(result['stream'], isFalse);
    });

    test('should generate secure headers', () {
      final headers = SecurityService.generateSecureHeaders(
        apiKey: 'test-api-key-123',
        userAgent: 'TestApp/1.0',
      );

      expect(headers['Content-Type'], equals('application/json'));
      expect(headers['Accept'], equals('application/json'));
      expect(headers['Authorization'], equals('Bearer test-api-key-123'));
      expect(headers['User-Agent'], equals('TestApp/1.0'));
      expect(headers['X-Requested-With'], equals('XMLHttpRequest'));
      expect(headers['Cache-Control'], contains('no-cache'));
    });

    test('should sanitize text for display', () {
      final input = 'Hello\x00world\r\n\r\n\r\nTest\x1F';
      final sanitized = SecurityService.sanitizeForDisplay(input);
      
      expect(sanitized, equals('Helloworld\n\nTest'));
    });
  });
}
