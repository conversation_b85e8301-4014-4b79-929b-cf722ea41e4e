import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ai_chat_app/main.dart';
import '../helpers/mock_api_server.dart';

void main() {
  group('Complete User Journey Tests', () {
    late MockApiServer mockServer;

    setUpAll(() async {
      mockServer = MockApiServer();
      await mockServer.start();
      
      // Set up environment variables for testing
      Platform.environment['API_BASE_URL'] = mockServer.baseUrl;
      Platform.environment['API_KEY'] = 'test-api-key-journey';
      Platform.environment['AGENT_ID'] = 'test-agent-journey';
      Platform.environment['DEBUG_MODE'] = 'true';
    });

    tearDownAll(() async {
      await mockServer.stop();
    });

    setUp(() {
      mockServer.reset();
    });

    testWidgets('Complete user journey: App launch to conversation', (WidgetTester tester) async {
      // Launch the app
      await tester.pumpWidget(const MyApp());
      
      // Should show loading screen initially
      expect(find.text('Initializing AI Chat Assistant...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      
      // Wait for configuration validation and app initialization
      await tester.pumpAndSettle(const Duration(seconds: 3));
      
      // Should now show the chat screen
      expect(find.text('AI Chat'), findsOneWidget);
      expect(find.text('Connected'), findsOneWidget);
      
      // Should display welcome message
      expect(find.textContaining('Welcome!'), findsOneWidget);
      
      // Should have enabled chat input
      final textField = find.byType(TextField);
      expect(textField, findsOneWidget);
      
      // Test sending first message
      await tester.enterText(textField, 'Hello, this is my first message!');
      await tester.testTextInput.receiveAction(TextInputAction.send);
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Should display user message and AI response
      expect(find.text('Hello, this is my first message!'), findsOneWidget);
      expect(find.textContaining('Thank you for your message'), findsOneWidget);
      
      // Test sending a message that triggers structured data
      await tester.enterText(textField, 'Show me some table data please');
      await tester.testTextInput.receiveAction(TextInputAction.send);
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Should display table data
      expect(find.text('Here is the requested data in table format:'), findsOneWidget);
      expect(find.text('Alice'), findsOneWidget);
      expect(find.text('Bob'), findsOneWidget);
      expect(find.text('Charlie'), findsOneWidget);
      
      // Test help functionality
      await tester.enterText(textField, 'What can you help me with?');
      await tester.testTextInput.receiveAction(TextInputAction.send);
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Should display help response
      expect(find.textContaining('I can help you with various tasks'), findsOneWidget);
      
      // Test session restart functionality
      final refreshButton = find.byIcon(Icons.refresh);
      expect(refreshButton, findsOneWidget);
      
      await tester.tap(refreshButton);
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Should have new session with only welcome message
      expect(find.textContaining('Welcome!'), findsOneWidget);
      // Previous messages should be cleared
      expect(find.text('Hello, this is my first message!'), findsNothing);
      
      // Test that new session works
      await tester.enterText(textField, 'Testing new session');
      await tester.testTextInput.receiveAction(TextInputAction.send);
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      expect(find.text('Testing new session'), findsOneWidget);
      expect(find.textContaining('Thank you for your message'), findsOneWidget);
    });

    testWidgets('User journey with network errors and recovery', (WidgetTester tester) async {
      // Enable network errors
      mockServer.simulateErrors = true;
      mockServer.errorRate = 1.0; // 100% error rate
      
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle(const Duration(seconds: 3));
      
      // Should show error state
      expect(find.text('Failed to initialize chat'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
      
      // Disable errors and retry
      mockServer.simulateErrors = false;
      final retryButton = find.text('Retry');
      await tester.tap(retryButton);
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Should recover and show welcome message
      expect(find.textContaining('Welcome!'), findsOneWidget);
      expect(find.text('Connected'), findsOneWidget);
      
      // Test message sending with intermittent errors
      mockServer.simulateErrors = true;
      mockServer.errorRate = 0.5; // 50% error rate
      
      final textField = find.byType(TextField);
      
      // Send multiple messages to test error handling
      for (int i = 1; i <= 3; i++) {
        await tester.enterText(textField, 'Test message $i');
        await tester.testTextInput.receiveAction(TextInputAction.send);
        await tester.pumpAndSettle(const Duration(seconds: 2));
        
        // Message should appear (either successful or failed)
        expect(find.text('Test message $i'), findsOneWidget);
      }
      
      // Should have some successful and some failed messages
      // (exact count depends on random error simulation)
      expect(find.textContaining('Test message'), findsWidgets);
    });

    testWidgets('User journey with configuration errors', (WidgetTester tester) async {
      // Clear environment variables to simulate configuration error
      final originalApiKey = Platform.environment['API_KEY'];
      Platform.environment.remove('API_KEY');
      
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Should show configuration error screen
      expect(find.text('Configuration Error'), findsOneWidget);
      expect(find.text('Configuration Required'), findsOneWidget);
      expect(find.text('Setup Instructions:'), findsOneWidget);
      expect(find.textContaining('API key is required'), findsOneWidget);
      
      // Should show retry button
      expect(find.text('Retry'), findsOneWidget);
      
      // Should show configuration details in debug mode
      expect(find.text('Details'), findsOneWidget);
      
      // Test configuration details dialog
      await tester.tap(find.text('Details'));
      await tester.pumpAndSettle();
      
      expect(find.text('Configuration Details'), findsOneWidget);
      expect(find.text('Close'), findsOneWidget);
      
      await tester.tap(find.text('Close'));
      await tester.pumpAndSettle();
      
      // Restore API key and retry
      Platform.environment['API_KEY'] = originalApiKey!;
      await tester.tap(find.text('Retry'));
      await tester.pumpAndSettle(const Duration(seconds: 3));
      
      // Should now show the chat screen
      expect(find.text('AI Chat'), findsOneWidget);
      expect(find.textContaining('Welcome!'), findsOneWidget);
    });

    testWidgets('Accessibility features work correctly', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle(const Duration(seconds: 3));
      
      // Test semantic labels and accessibility
      expect(find.byType(Semantics), findsWidgets);
      
      // Test that text field has proper semantics
      final textField = find.byType(TextField);
      expect(textField, findsOneWidget);
      
      final textFieldWidget = tester.widget<TextField>(textField);
      expect(textFieldWidget.decoration?.hintText, isNotNull);
      
      // Test that buttons have proper semantics
      final refreshButton = find.byIcon(Icons.refresh);
      expect(refreshButton, findsOneWidget);
      
      // Test keyboard navigation
      await tester.enterText(textField, 'Accessibility test message');
      await tester.testTextInput.receiveAction(TextInputAction.send);
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      expect(find.text('Accessibility test message'), findsOneWidget);
    });

    testWidgets('Theme switching works correctly', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle(const Duration(seconds: 3));
      
      // Test that app uses system theme by default
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
      expect(materialApp.themeMode, equals(ThemeMode.system));
      expect(materialApp.theme, isNotNull);
      expect(materialApp.darkTheme, isNotNull);
      
      // Verify theme properties
      expect(materialApp.theme!.useMaterial3, isTrue);
      expect(materialApp.darkTheme!.useMaterial3, isTrue);
    });

    testWidgets('Error widget displays correctly for unhandled errors', (WidgetTester tester) async {
      // This test would require injecting an error, which is complex in this context
      // In a real scenario, you might test this by mocking a service to throw an error
      
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle(const Duration(seconds: 3));
      
      // Verify that the app has error handling configured
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
      expect(materialApp.builder, isNotNull);
    });

    testWidgets('App handles rapid user interactions gracefully', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle(const Duration(seconds: 3));
      
      final textField = find.byType(TextField);
      
      // Rapidly enter text and send messages
      for (int i = 1; i <= 5; i++) {
        await tester.enterText(textField, 'Rapid message $i');
        await tester.testTextInput.receiveAction(TextInputAction.send);
        // Don't wait for full settlement to simulate rapid interaction
        await tester.pump(const Duration(milliseconds: 100));
      }
      
      // Wait for all operations to complete
      await tester.pumpAndSettle(const Duration(seconds: 5));
      
      // Should handle all messages without crashing
      expect(find.textContaining('Rapid message'), findsWidgets);
    });

    test('App configuration validation works correctly', () {
      // Test that configuration validation catches common issues
      final originalApiKey = Platform.environment['API_KEY'];
      final originalBaseUrl = Platform.environment['API_BASE_URL'];
      final originalAgentId = Platform.environment['AGENT_ID'];
      
      try {
        // Test missing API key
        Platform.environment.remove('API_KEY');
        expect(() => SecurityService.validateConfiguration(), throwsException);
        
        // Restore API key, test invalid base URL
        Platform.environment['API_KEY'] = 'test-key';
        Platform.environment['API_BASE_URL'] = 'invalid-url';
        expect(() => SecurityService.validateConfiguration(), throwsException);
        
        // Test valid configuration
        Platform.environment['API_BASE_URL'] = 'https://api.example.com';
        Platform.environment['AGENT_ID'] = 'test-agent';
        // Should not throw
        SecurityService.validateConfiguration();
        
      } finally {
        // Restore original values
        if (originalApiKey != null) Platform.environment['API_KEY'] = originalApiKey;
        if (originalBaseUrl != null) Platform.environment['API_BASE_URL'] = originalBaseUrl;
        if (originalAgentId != null) Platform.environment['AGENT_ID'] = originalAgentId;
      }
    });
  });
}
