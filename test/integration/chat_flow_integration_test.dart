import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:ai_chat_app/features/chat/providers/chat_provider.dart';
import 'package:ai_chat_app/features/chat/repositories/chat_repository.dart';
import 'package:ai_chat_app/features/chat/services/chat_api_service.dart';
import 'package:ai_chat_app/features/chat/services/connectivity_service.dart';
import 'package:ai_chat_app/features/chat/screens/chat_screen.dart';
import 'package:ai_chat_app/features/chat/widgets/message_bubble.dart';
import 'package:ai_chat_app/features/chat/widgets/chat_input.dart';
import 'package:ai_chat_app/features/chat/models/message.dart';
import '../helpers/mock_api_server.dart';

void main() {
  group('Chat Flow Integration Tests', () {
    late MockApiServer mockServer;
    late ChatProvider chatProvider;

    setUpAll(() async {
      // Start mock API server
      mockServer = MockApiServer();
      await mockServer.start();
      
      // Set environment variables for testing
      Platform.environment['API_BASE_URL'] = mockServer.baseUrl;
      Platform.environment['API_KEY'] = 'test-api-key-for-integration';
      Platform.environment['AGENT_ID'] = 'test-agent-integration';
    });

    tearDownAll(() async {
      await mockServer.stop();
    });

    setUp(() {
      mockServer.reset();
      chatProvider = ChatProvider(
        repository: ApiChatRepository(
          apiService: ChatApiService(
            baseUrl: mockServer.baseUrl,
            apiKey: 'test-api-key-for-integration',
          ),
        ),
        agentId: 'test-agent-integration',
      );
    });

    tearDown(() {
      chatProvider.dispose();
    });

    Widget createTestApp() {
      return MaterialApp(
        home: ConnectivityProvider(
          child: ChangeNotifierProvider<ChatProvider>.value(
            value: chatProvider,
            child: const ChatScreen(),
          ),
        ),
      );
    }

    testWidgets('should complete full chat session initialization flow', (WidgetTester tester) async {
      await tester.pumpWidget(createTestApp());
      
      // Wait for session initialization
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Should display welcome message
      expect(find.text('Welcome! I\'m your AI assistant. How can I help you today?'), findsOneWidget);
      
      // Should show connected status
      expect(find.text('Connected'), findsOneWidget);
      
      // Should have chat input enabled
      expect(find.byType(TextField), findsOneWidget);
      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.enabled, isTrue);
      
      // Verify session was created
      expect(chatProvider.hasSession, isTrue);
      expect(chatProvider.sessionId, isNotNull);
      expect(chatProvider.messages.length, equals(1));
      expect(chatProvider.messages.first.type, equals(MessageType.assistant));
    });

    testWidgets('should handle complete message sending and receiving flow', (WidgetTester tester) async {
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Send a message
      final textField = find.byType(TextField);
      await tester.enterText(textField, 'Hello, how are you?');
      await tester.testTextInput.receiveAction(TextInputAction.send);
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Should have user message and AI response
      expect(chatProvider.messages.length, equals(3)); // welcome + user + AI
      
      // Check user message
      final userMessage = chatProvider.messages[1];
      expect(userMessage.type, equals(MessageType.user));
      expect(userMessage.content, equals('Hello, how are you?'));
      expect(userMessage.status, equals(MessageStatus.sent));
      
      // Check AI response
      final aiMessage = chatProvider.messages[2];
      expect(aiMessage.type, equals(MessageType.assistant));
      expect(aiMessage.content, contains('Thank you for your message'));
      expect(aiMessage.status, equals(MessageStatus.sent));
      
      // Verify UI shows both messages
      expect(find.text('Hello, how are you?'), findsOneWidget);
      expect(find.textContaining('Thank you for your message'), findsOneWidget);
    });

    testWidgets('should handle structured data responses correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Send a message that triggers table response
      final textField = find.byType(TextField);
      await tester.enterText(textField, 'Show me some table data');
      await tester.testTextInput.receiveAction(TextInputAction.send);
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Should display table data
      expect(find.text('Here is the requested data in table format:'), findsOneWidget);
      expect(find.text('Alice'), findsOneWidget);
      expect(find.text('Bob'), findsOneWidget);
      expect(find.text('Charlie'), findsOneWidget);
      expect(find.text('New York'), findsOneWidget);
      
      // Should display reference data
      expect(find.text('Reference Data'), findsOneWidget);
      expect(find.text('data_source:'), findsOneWidget);
      expect(find.text('mock_database'), findsOneWidget);
      
      // Should display parameters
      expect(find.text('Parameters'), findsOneWidget);
      expect(find.text('table_data_requested'), findsOneWidget);
    });

    testWidgets('should handle multiple message conversation flow', (WidgetTester tester) async {
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      final textField = find.byType(TextField);
      
      // Send first message
      await tester.enterText(textField, 'First message');
      await tester.testTextInput.receiveAction(TextInputAction.send);
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Send second message
      await tester.enterText(textField, 'Second message');
      await tester.testTextInput.receiveAction(TextInputAction.send);
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Send third message
      await tester.enterText(textField, 'Third message');
      await tester.testTextInput.receiveAction(TextInputAction.send);
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Should have all messages (welcome + 3 user + 3 AI = 7 total)
      expect(chatProvider.messages.length, equals(7));
      
      // Verify conversation flow
      expect(find.text('First message'), findsOneWidget);
      expect(find.text('Second message'), findsOneWidget);
      expect(find.text('Third message'), findsOneWidget);
      
      // Verify AI responses reference conversation turn
      expect(find.textContaining('response #1'), findsOneWidget);
      expect(find.textContaining('response #2'), findsOneWidget);
      expect(find.textContaining('response #3'), findsOneWidget);
    });

    testWidgets('should handle session restart flow', (WidgetTester tester) async {
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Send a message
      final textField = find.byType(TextField);
      await tester.enterText(textField, 'Test message');
      await tester.testTextInput.receiveAction(TextInputAction.send);
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      final originalSessionId = chatProvider.sessionId;
      expect(chatProvider.messages.length, equals(3)); // welcome + user + AI
      
      // Restart session
      final refreshButton = find.byIcon(Icons.refresh);
      await tester.tap(refreshButton);
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Should have new session
      expect(chatProvider.sessionId, isNot(equals(originalSessionId)));
      expect(chatProvider.messages.length, equals(1)); // only new welcome message
      expect(chatProvider.messages.first.content, contains('Welcome!'));
    });

    testWidgets('should handle network error scenarios', (WidgetTester tester) async {
      // Enable error simulation
      mockServer.simulateErrors = true;
      mockServer.errorRate = 1.0; // 100% error rate
      
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle(const Duration(seconds: 3));
      
      // Should show error state
      expect(find.text('Failed to initialize chat'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
      
      // Disable errors and retry
      mockServer.simulateErrors = false;
      final retryButton = find.text('Retry');
      await tester.tap(retryButton);
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Should recover and show welcome message
      expect(find.text('Welcome! I\'m your AI assistant. How can I help you today?'), findsOneWidget);
    });

    testWidgets('should handle message sending errors with retry', (WidgetTester tester) async {
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Enable errors after session initialization
      mockServer.simulateErrors = true;
      mockServer.errorRate = 1.0;
      
      // Try to send a message
      final textField = find.byType(TextField);
      await tester.enterText(textField, 'This will fail');
      await tester.testTextInput.receiveAction(TextInputAction.send);
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Should show failed message with retry button
      expect(chatProvider.messages.length, equals(2)); // welcome + failed user message
      final failedMessage = chatProvider.messages.last;
      expect(failedMessage.status, equals(MessageStatus.failed));
      
      // Should show error banner
      expect(find.textContaining('Failed to send message'), findsOneWidget);
      
      // Find and tap retry button on the failed message
      final retryButtons = find.text('Retry');
      expect(retryButtons, findsWidgets);
      
      // Disable errors and retry
      mockServer.simulateErrors = false;
      await tester.tap(retryButtons.first);
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Should have successful message and AI response
      expect(chatProvider.messages.length, equals(3)); // welcome + user + AI
      expect(chatProvider.messages[1].status, equals(MessageStatus.sent));
    });

    testWidgets('should handle timeout scenarios', (WidgetTester tester) async {
      // Enable timeout simulation
      mockServer.simulateTimeouts = true;
      
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle(const Duration(seconds: 35)); // Wait for timeout
      
      // Should show timeout error
      expect(find.text('Failed to initialize chat'), findsOneWidget);
      expect(find.textContaining('Request timed out'), findsOneWidget);
    });

    test('should maintain conversation history correctly', () async {
      // Initialize session
      await chatProvider.initializeSession();
      expect(chatProvider.hasSession, isTrue);
      
      // Send multiple messages
      await chatProvider.sendMessage('First message');
      await chatProvider.sendMessage('Second message');
      await chatProvider.sendMessage('Third message');
      
      // Verify conversation history
      expect(chatProvider.messages.length, equals(7)); // welcome + 3 user + 3 AI
      
      // Verify message order and content
      expect(chatProvider.messages[0].type, equals(MessageType.assistant)); // welcome
      expect(chatProvider.messages[1].type, equals(MessageType.user));
      expect(chatProvider.messages[1].content, equals('First message'));
      expect(chatProvider.messages[2].type, equals(MessageType.assistant));
      expect(chatProvider.messages[3].type, equals(MessageType.user));
      expect(chatProvider.messages[3].content, equals('Second message'));
      expect(chatProvider.messages[4].type, equals(MessageType.assistant));
      expect(chatProvider.messages[5].type, equals(MessageType.user));
      expect(chatProvider.messages[5].content, equals('Third message'));
      expect(chatProvider.messages[6].type, equals(MessageType.assistant));
      
      // Verify server-side conversation history
      final serverHistory = mockServer.getConversationHistory(chatProvider.sessionId!);
      expect(serverHistory, isNotNull);
      expect(serverHistory!.length, equals(6)); // 3 user + 3 assistant (no welcome in history)
    });

    test('should handle different API response types', () async {
      await chatProvider.initializeSession();
      
      // Test help response
      await chatProvider.sendMessage('What can you do?');
      final helpResponse = chatProvider.messages.last;
      expect(helpResponse.content, contains('I can help you with various tasks'));
      expect(helpResponse.metadata?['reference'], isNotNull);
      expect(helpResponse.metadata?['param'], contains('help_requested'));
      
      // Test table data response
      await chatProvider.sendMessage('Show me table data');
      final tableResponse = chatProvider.messages.last;
      expect(tableResponse.content, contains('Here is the requested data'));
      expect(tableResponse.metadata?['table'], isNotNull);
      
      // Test error simulation response
      await chatProvider.sendMessage('Simulate an error');
      final errorResponse = chatProvider.messages.last;
      expect(errorResponse.content, contains('I encountered an issue'));
      expect(errorResponse.metadata?['reference']?['error_type'], equals('processing_error'));
    });
  });
}
