import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:ai_chat_app/features/chat/providers/chat_provider.dart';
import 'package:ai_chat_app/features/chat/repositories/chat_repository.dart';
import 'package:ai_chat_app/features/chat/services/chat_api_service.dart';
import 'package:ai_chat_app/features/chat/services/connectivity_service.dart';
import 'package:ai_chat_app/features/chat/screens/chat_screen.dart';
import 'package:ai_chat_app/features/chat/models/message.dart';
import '../helpers/mock_api_server.dart';

void main() {
  group('Performance and Edge Case Tests', () {
    late MockApiServer mockServer;
    late ChatProvider chatProvider;

    setUpAll(() async {
      mockServer = MockApiServer();
      await mockServer.start();
      
      Platform.environment['API_BASE_URL'] = mockServer.baseUrl;
      Platform.environment['API_KEY'] = 'test-api-key-performance';
      Platform.environment['AGENT_ID'] = 'test-agent-performance';
    });

    tearDownAll(() async {
      await mockServer.stop();
    });

    setUp(() {
      mockServer.reset();
      // Disable network delay for performance tests
      mockServer.simulateNetworkDelay = false;
      
      chatProvider = ChatProvider(
        repository: ApiChatRepository(
          apiService: ChatApiService(
            baseUrl: mockServer.baseUrl,
            apiKey: 'test-api-key-performance',
          ),
        ),
        agentId: 'test-agent-performance',
      );
    });

    tearDown(() {
      chatProvider.dispose();
    });

    Widget createTestApp() {
      return MaterialApp(
        home: ConnectivityProvider(
          child: ChangeNotifierProvider<ChatProvider>.value(
            value: chatProvider,
            child: const ChatScreen(),
          ),
        ),
      );
    }

    group('Long Conversation Handling', () {
      test('should handle 100+ message conversation efficiently', () async {
        await chatProvider.initializeSession();
        
        final stopwatch = Stopwatch()..start();
        
        // Send 100 messages
        for (int i = 1; i <= 100; i++) {
          await chatProvider.sendMessage('Message number $i');
          
          // Check memory usage doesn't grow excessively
          expect(chatProvider.messages.length, equals(1 + (i * 2))); // welcome + i user + i AI
        }
        
        stopwatch.stop();
        
        // Should complete within reasonable time (adjust based on performance requirements)
        expect(stopwatch.elapsedMilliseconds, lessThan(10000)); // 10 seconds
        
        // Verify all messages are present
        expect(chatProvider.messages.length, equals(201)); // 1 welcome + 100 user + 100 AI
        
        // Verify message order is maintained
        expect(chatProvider.messages[1].content, equals('Message number 1'));
        expect(chatProvider.messages[199].content, equals('Message number 100'));
      });

      testWidgets('should handle long conversation UI rendering efficiently', (WidgetTester tester) async {
        await tester.pumpWidget(createTestApp());
        await tester.pumpAndSettle();
        
        final textField = find.byType(TextField);
        
        // Send 50 messages and measure rendering performance
        final stopwatch = Stopwatch()..start();
        
        for (int i = 1; i <= 50; i++) {
          await tester.enterText(textField, 'UI test message $i');
          await tester.testTextInput.receiveAction(TextInputAction.send);
          await tester.pumpAndSettle();
          
          // Verify UI can handle the growing message list
          expect(find.text('UI test message $i'), findsOneWidget);
        }
        
        stopwatch.stop();
        
        // UI should remain responsive
        expect(stopwatch.elapsedMilliseconds, lessThan(30000)); // 30 seconds for UI operations
        
        // Verify scroll position is at bottom
        final listView = tester.widget<ListView>(find.byType(ListView));
        expect(listView.controller, isNotNull);
      });

      test('should manage memory efficiently with large conversations', () async {
        await chatProvider.initializeSession();
        
        // Create messages with large content
        final largeContent = 'A' * 1000; // 1KB per message
        
        for (int i = 1; i <= 200; i++) {
          await chatProvider.sendMessage('$largeContent - Message $i');
        }
        
        // Verify all messages are stored
        expect(chatProvider.messages.length, equals(401)); // 1 welcome + 200 user + 200 AI
        
        // Memory usage should be reasonable (this is a basic check)
        // In a real app, you might want to implement message pagination or cleanup
        final totalContentSize = chatProvider.messages
            .map((m) => m.content.length)
            .reduce((a, b) => a + b);
        
        // Should be roughly 200KB of message content plus responses
        expect(totalContentSize, greaterThan(200000));
        expect(totalContentSize, lessThan(1000000)); // Less than 1MB
      });
    });

    group('Rapid Message Sending', () {
      test('should handle rapid sequential message sending', () async {
        await chatProvider.initializeSession();
        
        final futures = <Future>[];
        final stopwatch = Stopwatch()..start();
        
        // Send 20 messages rapidly
        for (int i = 1; i <= 20; i++) {
          futures.add(chatProvider.sendMessage('Rapid message $i'));
        }
        
        // Wait for all messages to complete
        await Future.wait(futures);
        stopwatch.stop();
        
        // All messages should be processed
        expect(chatProvider.messages.length, equals(41)); // 1 welcome + 20 user + 20 AI
        
        // Should complete within reasonable time
        expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // 5 seconds
        
        // Verify no messages were lost or duplicated
        final userMessages = chatProvider.messages
            .where((m) => m.type == MessageType.user)
            .toList();
        expect(userMessages.length, equals(20));
        
        for (int i = 1; i <= 20; i++) {
          expect(userMessages.any((m) => m.content == 'Rapid message $i'), isTrue);
        }
      });

      testWidgets('should prevent duplicate message sending', (WidgetTester tester) async {
        await tester.pumpWidget(createTestApp());
        await tester.pumpAndSettle();
        
        final textField = find.byType(TextField);
        
        // Enter text
        await tester.enterText(textField, 'Test duplicate prevention');
        
        // Rapidly tap send multiple times
        for (int i = 0; i < 5; i++) {
          await tester.testTextInput.receiveAction(TextInputAction.send);
        }
        
        await tester.pumpAndSettle();
        
        // Should only have one user message (plus welcome and AI response)
        final userMessages = chatProvider.messages
            .where((m) => m.type == MessageType.user)
            .toList();
        expect(userMessages.length, equals(1));
        expect(userMessages.first.content, equals('Test duplicate prevention'));
      });

      test('should handle concurrent message sending gracefully', () async {
        await chatProvider.initializeSession();
        
        // Start multiple message sending operations concurrently
        final futures = [
          chatProvider.sendMessage('Concurrent message 1'),
          chatProvider.sendMessage('Concurrent message 2'),
          chatProvider.sendMessage('Concurrent message 3'),
        ];
        
        // Wait for all to complete
        await Future.wait(futures);
        
        // All messages should be processed (order may vary due to concurrency)
        expect(chatProvider.messages.length, equals(7)); // 1 welcome + 3 user + 3 AI
        
        final userMessages = chatProvider.messages
            .where((m) => m.type == MessageType.user)
            .map((m) => m.content)
            .toList();
        
        expect(userMessages, containsAll([
          'Concurrent message 1',
          'Concurrent message 2',
          'Concurrent message 3',
        ]));
      });
    });

    group('App Lifecycle and Session Persistence', () {
      testWidgets('should handle app backgrounding and foregrounding', (WidgetTester tester) async {
        await tester.pumpWidget(createTestApp());
        await tester.pumpAndSettle();
        
        // Send a message
        final textField = find.byType(TextField);
        await tester.enterText(textField, 'Before backgrounding');
        await tester.testTextInput.receiveAction(TextInputAction.send);
        await tester.pumpAndSettle();
        
        final originalSessionId = chatProvider.sessionId;
        expect(chatProvider.messages.length, equals(3)); // welcome + user + AI
        
        // Simulate app going to background
        tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(
          SystemChannels.lifecycle,
          (MethodCall methodCall) async {
            if (methodCall.method == 'routeUpdated') {
              return null;
            }
            return null;
          },
        );
        
        // Simulate lifecycle state change to paused
        await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
          SystemChannels.lifecycle.name,
          SystemChannels.lifecycle.codec.encodeMessage('AppLifecycleState.paused'),
          (data) {},
        );
        
        await tester.pumpAndSettle();
        
        // Session should still be active
        expect(chatProvider.sessionId, equals(originalSessionId));
        expect(chatProvider.messages.length, equals(3));
        
        // Simulate app coming back to foreground
        await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
          SystemChannels.lifecycle.name,
          SystemChannels.lifecycle.codec.encodeMessage('AppLifecycleState.resumed'),
          (data) {},
        );
        
        await tester.pumpAndSettle();
        
        // Should still be able to send messages
        await tester.enterText(textField, 'After foregrounding');
        await tester.testTextInput.receiveAction(TextInputAction.send);
        await tester.pumpAndSettle();
        
        expect(chatProvider.messages.length, equals(5)); // welcome + 2 user + 2 AI
        expect(chatProvider.sessionId, equals(originalSessionId));
      });

      test('should reinitialize session if lost during backgrounding', () async {
        await chatProvider.initializeSession();
        final originalSessionId = chatProvider.sessionId;
        
        // Simulate session loss (e.g., server restart)
        await chatProvider.clearSession();
        expect(chatProvider.hasSession, isFalse);
        
        // Simulate app lifecycle resume
        await chatProvider.initializeSession();
        
        // Should have new session
        expect(chatProvider.hasSession, isTrue);
        expect(chatProvider.sessionId, isNot(equals(originalSessionId)));
        expect(chatProvider.messages.length, equals(1)); // new welcome message
      });
    });

    group('Network Interruption Scenarios', () {
      test('should handle network interruption during session initialization', () async {
        // Simulate network failure
        mockServer.simulateErrors = true;
        mockServer.errorRate = 1.0;
        
        // Try to initialize session
        await chatProvider.initializeSession();
        
        // Should have error state
        expect(chatProvider.hasSession, isFalse);
        expect(chatProvider.error, isNotNull);
        expect(chatProvider.chatError, isNotNull);
        
        // Restore network and retry
        mockServer.simulateErrors = false;
        await chatProvider.initializeSession();
        
        // Should recover
        expect(chatProvider.hasSession, isTrue);
        expect(chatProvider.error, isNull);
        expect(chatProvider.messages.length, equals(1));
      });

      test('should handle network interruption during message sending', () async {
        await chatProvider.initializeSession();
        
        // Send successful message first
        await chatProvider.sendMessage('First message');
        expect(chatProvider.messages.length, equals(3));
        
        // Simulate network failure
        mockServer.simulateErrors = true;
        mockServer.errorRate = 1.0;
        
        // Try to send message during network failure
        await chatProvider.sendMessage('Failed message');
        
        // Should have failed message
        expect(chatProvider.messages.length, equals(4)); // welcome + user + AI + failed user
        final failedMessage = chatProvider.messages.last;
        expect(failedMessage.status, equals(MessageStatus.failed));
        expect(failedMessage.content, equals('Failed message'));
        
        // Restore network and retry
        mockServer.simulateErrors = false;
        await chatProvider.retryMessage(failedMessage.id);
        
        // Should recover and get AI response
        expect(chatProvider.messages.length, equals(5)); // + AI response
        expect(chatProvider.messages[3].status, equals(MessageStatus.sent));
      });

      test('should handle intermittent network issues', () async {
        await chatProvider.initializeSession();
        
        // Simulate intermittent network issues (50% failure rate)
        mockServer.simulateErrors = true;
        mockServer.errorRate = 0.5;
        
        int successCount = 0;
        int failureCount = 0;
        
        // Send 20 messages with intermittent failures
        for (int i = 1; i <= 20; i++) {
          await chatProvider.sendMessage('Intermittent test $i');
          
          final lastMessage = chatProvider.messages
              .where((m) => m.type == MessageType.user)
              .last;
          
          if (lastMessage.status == MessageStatus.sent) {
            successCount++;
          } else if (lastMessage.status == MessageStatus.failed) {
            failureCount++;
          }
        }
        
        // Should have some successes and some failures
        expect(successCount, greaterThan(0));
        expect(failureCount, greaterThan(0));
        expect(successCount + failureCount, equals(20));
        
        // Total messages should include welcome + user messages + successful AI responses
        expect(chatProvider.messages.length, equals(1 + 20 + successCount));
      });
    });

    group('Edge Cases and Error Recovery', () {
      test('should handle extremely long messages', () async {
        await chatProvider.initializeSession();
        
        // Create a very long message (near the limit)
        final longMessage = 'A' * 9999; // Just under 10KB limit
        
        await chatProvider.sendMessage(longMessage);
        
        // Should handle long message successfully
        expect(chatProvider.messages.length, equals(3));
        final userMessage = chatProvider.messages[1];
        expect(userMessage.content.length, equals(9999));
        expect(userMessage.status, equals(MessageStatus.sent));
      });

      test('should handle special characters and unicode', () async {
        await chatProvider.initializeSession();
        
        final specialMessage = '🚀 Hello! 你好 🌟 Émojis & spëcial chars: @#\$%^&*()';
        
        await chatProvider.sendMessage(specialMessage);
        
        expect(chatProvider.messages.length, equals(3));
        final userMessage = chatProvider.messages[1];
        expect(userMessage.content, equals(specialMessage));
        expect(userMessage.status, equals(MessageStatus.sent));
      });

      test('should handle rapid session restart scenarios', () async {
        // Rapidly restart sessions multiple times
        for (int i = 0; i < 10; i++) {
          await chatProvider.initializeSession();
          expect(chatProvider.hasSession, isTrue);
          
          await chatProvider.clearSession();
          expect(chatProvider.hasSession, isFalse);
        }
        
        // Final initialization should work
        await chatProvider.initializeSession();
        expect(chatProvider.hasSession, isTrue);
        expect(chatProvider.messages.length, equals(1));
      });

      test('should handle provider disposal during active operations', () async {
        await chatProvider.initializeSession();
        
        // Start a message sending operation
        final messageFuture = chatProvider.sendMessage('Test message');
        
        // Dispose provider while operation is in progress
        chatProvider.dispose();
        
        // Operation should complete gracefully (no exceptions thrown)
        await messageFuture;
        
        // Provider should be disposed
        expect(() => chatProvider.messages, throwsA(isA<AssertionError>()));
      });
    });
  });
}
