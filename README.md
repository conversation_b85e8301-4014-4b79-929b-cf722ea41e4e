# AI Chat Integration Flutter App

A Flutter application that integrates with an AI agent API to provide a chat interface.

## Project Structure

```
lib/
├── main.dart                           # App entry point
├── config/
│   └── chat_config.dart               # Configuration settings
└── features/
    └── chat/
        ├── models/                    # Data models
        │   ├── message.dart
        │   ├── chat_session.dart
        │   └── api_models.dart
        ├── services/                  # API services
        │   └── chat_api_service.dart
        ├── repositories/              # Data repositories
        │   └── chat_repository.dart
        ├── providers/                 # State management
        │   └── chat_provider.dart
        ├── screens/                   # UI screens
        │   └── chat_screen.dart
        └── widgets/                   # UI components
            └── message_bubble.dart
```

## Setup

1. Copy `.env.example` to `.env` and configure your API settings
2. Run `flutter pub get` to install dependencies
3. Run `flutter run` to start the app

## Dependencies

- **provider**: State management
- **http**: HTTP client for API calls
- **intl**: Internationalization support

## Configuration

Configure your API settings in the environment variables:
- `API_BASE_URL`: Base URL for the AI agent API
- `API_KEY`: Authentication key for API access
- `AGENT_ID`: Default agent ID for chat sessions