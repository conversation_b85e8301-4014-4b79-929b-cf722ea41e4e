import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:io';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'AI Chat with RAGFlow',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: const ChatScreen(),
    );
  }
}

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _controller = TextEditingController();
  final List<ChatMessage> _messages = [];
  bool _isLoading = false;
  String? _sessionId;
  
  // RAGFlow API Configuration
  final String _apiKey = Platform.environment['API_KEY'] ?? '';
  final String _baseUrl = Platform.environment['API_BASE_URL'] ?? 'https://api.ragflow.io';
  final String _agentId = Platform.environment['AGENT_ID'] ?? '';

  @override
  void initState() {
    super.initState();
    _initializeChat();
  }

  Future<void> _initializeChat() async {
    if (_apiKey.isEmpty || _agentId.isEmpty) {
      setState(() {
        _messages.add(ChatMessage(
          text: "⚠️ Configuration Required\n\n"
              "To connect to RAGFlow API, please set these environment variables:\n\n"
              "• API_KEY: Your RAGFlow API key\n"
              "• API_BASE_URL: RAGFlow server URL (default: https://api.ragflow.io)\n"
              "• AGENT_ID: Your RAGFlow agent ID\n\n"
              "Example:\n"
              "export API_KEY=\"your-api-key\"\n"
              "export API_BASE_URL=\"https://api.ragflow.io\"\n"
              "export AGENT_ID=\"your-agent-id\"\n\n"
              "Current configuration:\n"
              "• API_KEY: ${_apiKey.isEmpty ? '❌ Not set' : '✅ Set'}\n"
              "• API_BASE_URL: $_baseUrl\n"
              "• AGENT_ID: ${_agentId.isEmpty ? '❌ Not set' : '✅ Set'}",
          isUser: false,
          timestamp: DateTime.now(),
        ));
      });
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await _initializeSession();
      setState(() {
        _messages.add(ChatMessage(
          text: "✅ Connected to RAGFlow API!\n\n"
              "Session initialized successfully. You can now chat with the AI agent.\n\n"
              "Configuration:\n"
              "• Server: $_baseUrl\n"
              "• Agent ID: $_agentId\n"
              "• Session ID: $_sessionId\n\n"
              "Type your message to start chatting!",
          isUser: false,
          timestamp: DateTime.now(),
        ));
      });
    } catch (e) {
      setState(() {
        _messages.add(ChatMessage(
          text: "❌ Failed to connect to RAGFlow API\n\n"
              "Error: $e\n\n"
              "Please check:\n"
              "• Your API key is valid\n"
              "• The server URL is correct\n"
              "• Your agent ID exists\n"
              "• You have internet connection\n\n"
              "Current configuration:\n"
              "• Server: $_baseUrl\n"
              "• Agent ID: $_agentId",
          isUser: false,
          timestamp: DateTime.now(),
        ));
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _initializeSession() async {
    final response = await http.post(
      Uri.parse('$_baseUrl/completions'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_apiKey',
      },
      body: jsonEncode({
        'agent_id': _agentId,
        'stream': false,
      }),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data['code'] == 200 && data['data'] != null) {
        _sessionId = data['data']['session_id'];
        if (data['data']['answer'] != null) {
          setState(() {
            _messages.add(ChatMessage(
              text: data['data']['answer'],
              isUser: false,
              timestamp: DateTime.now(),
            ));
          });
        }
      } else {
        throw Exception('API returned error: ${data['message']}');
      }
    } else {
      throw Exception('HTTP ${response.statusCode}: ${response.body}');
    }
  }

  Future<void> _sendMessage() async {
    if (_controller.text.trim().isEmpty || _isLoading) return;

    final userMessage = _controller.text.trim();
    setState(() {
      _messages.add(ChatMessage(
        text: userMessage,
        isUser: true,
        timestamp: DateTime.now(),
      ));
      _isLoading = true;
    });

    _controller.clear();

    if (_sessionId == null) {
      setState(() {
        _messages.add(ChatMessage(
          text: "❌ No active session. Please restart the app to reconnect.",
          isUser: false,
          timestamp: DateTime.now(),
        ));
        _isLoading = false;
      });
      return;
    }

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: jsonEncode({
          'agent_id': _agentId,
          'question': userMessage,
          'session_id': _sessionId,
          'stream': false,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['code'] == 200 && data['data'] != null) {
          setState(() {
            _messages.add(ChatMessage(
              text: data['data']['answer'] ?? 'No response from AI',
              isUser: false,
              timestamp: DateTime.now(),
              metadata: data['data'],
            ));
          });
        } else {
          throw Exception('API returned error: ${data['message']}');
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      setState(() {
        _messages.add(ChatMessage(
          text: "❌ Failed to send message: $e\n\nTap to retry.",
          isUser: false,
          timestamp: DateTime.now(),
          isError: true,
          retryMessage: userMessage,
        ));
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _retryMessage(String message) {
    _controller.text = message;
    _sendMessage();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('RAGFlow AI Chat'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            margin: const EdgeInsets.only(right: 16),
            decoration: BoxDecoration(
              color: _sessionId != null ? Colors.green.withOpacity(0.2) : Colors.red.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.circle,
                  size: 8,
                  color: _sessionId != null ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 4),
                Text(
                  _sessionId != null ? 'Connected' : 'Disconnected',
                  style: TextStyle(
                    color: _sessionId != null ? Colors.green.shade700 : Colors.red.shade700,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                final message = _messages[index];
                return MessageBubble(
                  message: message,
                  onRetry: message.isError ? () => _retryMessage(message.retryMessage!) : null,
                );
              },
            ),
          ),
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  SizedBox(width: 8),
                  Text('AI is thinking...'),
                ],
              ),
            ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _controller,
                    enabled: !_isLoading && _sessionId != null,
                    decoration: InputDecoration(
                      hintText: _sessionId != null 
                          ? 'Type your message...' 
                          : 'Connect to RAGFlow first...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
                const SizedBox(width: 8),
                FloatingActionButton(
                  onPressed: _isLoading || _sessionId == null ? null : _sendMessage,
                  mini: true,
                  child: _isLoading 
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.send),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;
  final bool isError;
  final String? retryMessage;

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.metadata,
    this.isError = false,
    this.retryMessage,
  });
}

class MessageBubble extends StatelessWidget {
  final ChatMessage message;
  final VoidCallback? onRetry;

  const MessageBubble({super.key, required this.message, this.onRetry});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment:
            message.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isUser) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: message.isError 
                  ? Colors.red 
                  : Theme.of(context).colorScheme.primary,
              child: Icon(
                message.isError ? Icons.error : Icons.smart_toy,
                size: 16,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: GestureDetector(
              onTap: onRetry,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: message.isUser
                      ? Theme.of(context).colorScheme.primary
                      : message.isError
                          ? Colors.red.withOpacity(0.1)
                          : Theme.of(context).colorScheme.surfaceVariant,
                  borderRadius: BorderRadius.circular(16),
                  border: message.isError
                      ? Border.all(color: Colors.red.withOpacity(0.3))
                      : null,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      message.text,
                      style: TextStyle(
                        color: message.isUser
                            ? Theme.of(context).colorScheme.onPrimary
                            : message.isError
                                ? Colors.red.shade700
                                : Theme.of(context).colorScheme.onSurfaceVariant,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _formatTime(message.timestamp),
                          style: TextStyle(
                            color: message.isUser
                                ? Theme.of(context).colorScheme.onPrimary.withOpacity(0.7)
                                : Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.7),
                            fontSize: 12,
                          ),
                        ),
                        if (message.metadata?['session_id'] != null) ...[
                          const SizedBox(width: 8),
                          Icon(
                            Icons.verified,
                            size: 12,
                            color: Colors.green.withOpacity(0.7),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          if (message.isUser) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: Theme.of(context).colorScheme.secondary,
              child: const Icon(
                Icons.person,
                size: 16,
                color: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}
