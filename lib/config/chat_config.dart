/// Configuration class for AI Chat integration
class ChatConfig {
  // API Configuration
  static const String baseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'https://api.example.com', // Replace with actual API URL
  );

  static const String apiKey = String.fromEnvironment(
    'API_KEY',
    defaultValue: '', // API key should be provided via environment variable
  );

  static const String defaultAgentId = String.fromEnvironment(
    'AGENT_ID',
    defaultValue: 'default-agent', // Replace with actual agent ID
  );

  // Request Configuration
  static const int requestTimeoutSeconds = 30;
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 2);

  // UI Configuration
  static const int maxMessageLength = 1000;
  static const int messagesPerPage = 50;

  // Validation
  static bool get isConfigured =>
      apiKey.isNotEmpty && baseUrl.isNotEmpty && defaultAgentId.isNotEmpty;

  // API Endpoints
  static String get completionsEndpoint => '$baseUrl/completions';
  static String get sessionsEndpoint => '$baseUrl/sessions';
}
