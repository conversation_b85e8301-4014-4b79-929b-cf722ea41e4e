import 'dart:io';

/// Configuration validation result
class ConfigValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const ConfigValidationResult({
    required this.isValid,
    this.errors = const [],
    this.warnings = const [],
  });

  bool get hasErrors => errors.isNotEmpty;
  bool get hasWarnings => warnings.isNotEmpty;
}

/// Configuration class for AI Chat integration
class ChatConfig {
  // Private constructor to prevent instantiation
  ChatConfig._();

  // API Configuration with environment variable support
  static String get baseUrl => _getEnvironmentVariable(
        'API_BASE_URL',
        'CHAT_API_BASE_URL',
        defaultValue: 'https://api.ragflow.io',
      );

  static String get apiKey => _getEnvironmentVariable(
        'API_KEY',
        'CHAT_API_KEY',
        defaultValue: '',
      );

  static String get defaultAgentId => _getEnvironmentVariable(
        'AGENT_ID',
        'CHAT_AGENT_ID',
        defaultValue: 'default-agent',
      );

  // Request Configuration with environment variable support
  static int get requestTimeoutSeconds => _getIntEnvironmentVariable(
        'REQUEST_TIMEOUT_SECONDS',
        'CHAT_REQUEST_TIMEOUT',
        defaultValue: 30,
        minValue: 5,
        maxValue: 300,
      );

  static int get maxRetries => _getIntEnvironmentVariable(
        'MAX_RETRIES',
        'CHAT_MAX_RETRIES',
        defaultValue: 3,
        minValue: 0,
        maxValue: 10,
      );

  static Duration get retryDelay => Duration(
        seconds: _getIntEnvironmentVariable(
          'RETRY_DELAY_SECONDS',
          'CHAT_RETRY_DELAY',
          defaultValue: 2,
          minValue: 1,
          maxValue: 30,
        ),
      );

  // UI Configuration with environment variable support
  static int get maxMessageLength => _getIntEnvironmentVariable(
        'MAX_MESSAGE_LENGTH',
        'CHAT_MAX_MESSAGE_LENGTH',
        defaultValue: 1000,
        minValue: 100,
        maxValue: 10000,
      );

  static int get messagesPerPage => _getIntEnvironmentVariable(
        'MESSAGES_PER_PAGE',
        'CHAT_MESSAGES_PER_PAGE',
        defaultValue: 50,
        minValue: 10,
        maxValue: 200,
      );

  // Debug Configuration
  static bool get isDebugMode => _getBoolEnvironmentVariable(
        'DEBUG_MODE',
        'CHAT_DEBUG',
        defaultValue: false,
      );

  static bool get enableLogging => _getBoolEnvironmentVariable(
        'ENABLE_LOGGING',
        'CHAT_ENABLE_LOGGING',
        defaultValue: true,
      );

  // Security Configuration
  static bool get validateSslCertificates => _getBoolEnvironmentVariable(
        'VALIDATE_SSL_CERTIFICATES',
        'CHAT_VALIDATE_SSL',
        defaultValue: true,
      );

  static int get maxConcurrentRequests => _getIntEnvironmentVariable(
        'MAX_CONCURRENT_REQUESTS',
        'CHAT_MAX_CONCURRENT',
        defaultValue: 5,
        minValue: 1,
        maxValue: 20,
      );

  // API Endpoints
  static String get completionsEndpoint => '$baseUrl/completions';
  static String get sessionsEndpoint => '$baseUrl/sessions';

  // Validation
  static bool get isConfigured => validateConfiguration().isValid;

  /// Comprehensive configuration validation
  static ConfigValidationResult validateConfiguration() {
    final errors = <String>[];
    final warnings = <String>[];

    // Validate API key
    if (apiKey.isEmpty) {
      errors.add('API key is required. Set API_KEY or CHAT_API_KEY environment variable.');
    } else if (apiKey.length < 10) {
      warnings.add('API key seems too short. Please verify it is correct.');
    }

    // Validate base URL
    if (baseUrl.isEmpty) {
      errors.add('Base URL is required. Set API_BASE_URL or CHAT_API_BASE_URL environment variable.');
    } else {
      final uri = Uri.tryParse(baseUrl);
      if (uri == null || !uri.hasScheme || !uri.hasAuthority) {
        errors.add('Base URL is not a valid URL: $baseUrl');
      } else if (uri.scheme != 'https' && uri.scheme != 'http') {
        errors.add('Base URL must use HTTP or HTTPS protocol: $baseUrl');
      } else if (uri.scheme == 'http' && validateSslCertificates) {
        warnings.add('Using HTTP instead of HTTPS may be insecure: $baseUrl');
      }
    }

    // Validate agent ID
    if (defaultAgentId.isEmpty) {
      errors.add('Agent ID is required. Set AGENT_ID or CHAT_AGENT_ID environment variable.');
    } else if (defaultAgentId.length < 3) {
      warnings.add('Agent ID seems too short. Please verify it is correct.');
    }

    // Validate timeout settings
    if (requestTimeoutSeconds < 5) {
      warnings.add('Request timeout is very low (${requestTimeoutSeconds}s). This may cause frequent timeouts.');
    } else if (requestTimeoutSeconds > 120) {
      warnings.add('Request timeout is very high (${requestTimeoutSeconds}s). This may cause poor user experience.');
    }

    // Validate retry settings
    if (maxRetries > 5) {
      warnings.add('Max retries is high ($maxRetries). This may cause long delays on failures.');
    }

    return ConfigValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Get configuration summary for debugging
  static Map<String, dynamic> getConfigurationSummary({bool includeSensitive = false}) {
    return {
      'baseUrl': baseUrl,
      'apiKey': includeSensitive ? apiKey : _maskSensitiveValue(apiKey),
      'defaultAgentId': defaultAgentId,
      'requestTimeoutSeconds': requestTimeoutSeconds,
      'maxRetries': maxRetries,
      'retryDelaySeconds': retryDelay.inSeconds,
      'maxMessageLength': maxMessageLength,
      'messagesPerPage': messagesPerPage,
      'isDebugMode': isDebugMode,
      'enableLogging': enableLogging,
      'validateSslCertificates': validateSslCertificates,
      'maxConcurrentRequests': maxConcurrentRequests,
      'isConfigured': isConfigured,
    };
  }

  // Private helper methods for environment variable handling

  /// Get string environment variable with fallback options
  static String _getEnvironmentVariable(
    String primaryKey,
    String secondaryKey, {
    required String defaultValue,
  }) {
    // Try primary key first
    String? value = Platform.environment[primaryKey];
    if (value != null && value.isNotEmpty) {
      return value.trim();
    }

    // Try secondary key
    value = Platform.environment[secondaryKey];
    if (value != null && value.isNotEmpty) {
      return value.trim();
    }

    // Try const environment (compile-time)
    const constValue = String.fromEnvironment('');
    if (constValue.isNotEmpty) {
      return constValue;
    }

    return defaultValue;
  }

  /// Get integer environment variable with validation
  static int _getIntEnvironmentVariable(
    String primaryKey,
    String secondaryKey, {
    required int defaultValue,
    int? minValue,
    int? maxValue,
  }) {
    final stringValue = _getEnvironmentVariable(
      primaryKey,
      secondaryKey,
      defaultValue: defaultValue.toString(),
    );

    final intValue = int.tryParse(stringValue) ?? defaultValue;

    // Apply bounds checking
    if (minValue != null && intValue < minValue) {
      return minValue;
    }
    if (maxValue != null && intValue > maxValue) {
      return maxValue;
    }

    return intValue;
  }

  /// Get boolean environment variable
  static bool _getBoolEnvironmentVariable(
    String primaryKey,
    String secondaryKey, {
    required bool defaultValue,
  }) {
    final stringValue = _getEnvironmentVariable(
      primaryKey,
      secondaryKey,
      defaultValue: defaultValue.toString(),
    ).toLowerCase();

    switch (stringValue) {
      case 'true':
      case '1':
      case 'yes':
      case 'on':
        return true;
      case 'false':
      case '0':
      case 'no':
      case 'off':
        return false;
      default:
        return defaultValue;
    }
  }

  /// Mask sensitive values for logging
  static String _maskSensitiveValue(String value) {
    if (value.isEmpty) return '<empty>';
    if (value.length <= 4) return '*' * value.length;
    return '${value.substring(0, 2)}${'*' * (value.length - 4)}${value.substring(value.length - 2)}';
  }
}
