import 'dart:convert';

/// Validation result for input validation
class ValidationResult {
  final bool isValid;
  final String? error;
  final String? sanitizedValue;

  const ValidationResult({
    required this.isValid,
    this.error,
    this.sanitizedValue,
  });

  factory ValidationResult.valid(String sanitizedValue) {
    return ValidationResult(
      isValid: true,
      sanitizedValue: sanitizedValue,
    );
  }

  factory ValidationResult.invalid(String error) {
    return ValidationResult(
      isValid: false,
      error: error,
    );
  }
}

/// Security-focused input validator for chat application
class InputValidator {
  // Private constructor to prevent instantiation
  InputValidator._();

  // API Key validation patterns
  static final RegExp _apiKeyPattern = RegExp(r'^[a-zA-Z0-9\-_]{10,}$');
  static final RegExp _bearerTokenPattern = RegExp(r'^Bearer\s+[a-zA-Z0-9\-_\.]{10,}$');

  // Agent ID validation pattern
  static final RegExp _agentIdPattern = RegExp(r'^[a-zA-Z0-9\-_]{3,50}$');

  // Message content validation
  static const int _maxMessageLength = 10000;
  static const int _minMessageLength = 1;

  // Dangerous patterns to detect
  static final List<RegExp> _dangerousPatterns = [
    RegExp(r'<script[^>]*>.*?</script>', caseSensitive: false),
    RegExp(r'javascript:', caseSensitive: false),
    RegExp(r'on\w+\s*=', caseSensitive: false),
    RegExp(r'data:text/html', caseSensitive: false),
    RegExp(r'vbscript:', caseSensitive: false),
  ];

  // SQL injection patterns
  static final List<RegExp> _sqlInjectionPatterns = [
    RegExp(r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)', caseSensitive: false),
    RegExp(r'(\b(OR|AND)\s+\d+\s*=\s*\d+)', caseSensitive: false),
    RegExp(r'[\'";]', caseSensitive: false),
  ];

  /// Validate API key format and security
  static ValidationResult validateApiKey(String? apiKey) {
    if (apiKey == null || apiKey.isEmpty) {
      return ValidationResult.invalid('API key is required');
    }

    final trimmedKey = apiKey.trim();

    // Check minimum length
    if (trimmedKey.length < 10) {
      return ValidationResult.invalid('API key must be at least 10 characters long');
    }

    // Check maximum length (reasonable upper bound)
    if (trimmedKey.length > 200) {
      return ValidationResult.invalid('API key is too long (max 200 characters)');
    }

    // Check for Bearer token format
    if (trimmedKey.startsWith('Bearer ')) {
      if (!_bearerTokenPattern.hasMatch(trimmedKey)) {
        return ValidationResult.invalid('Invalid Bearer token format');
      }
      return ValidationResult.valid(trimmedKey);
    }

    // Check standard API key format
    if (!_apiKeyPattern.hasMatch(trimmedKey)) {
      return ValidationResult.invalid('API key contains invalid characters. Only alphanumeric, hyphens, and underscores are allowed');
    }

    // Check for obviously fake or test keys
    final lowerKey = trimmedKey.toLowerCase();
    if (lowerKey.contains('test') || lowerKey.contains('fake') || lowerKey.contains('demo')) {
      return ValidationResult.invalid('Test or demo API keys are not allowed in production');
    }

    return ValidationResult.valid(trimmedKey);
  }

  /// Validate agent ID format
  static ValidationResult validateAgentId(String? agentId) {
    if (agentId == null || agentId.isEmpty) {
      return ValidationResult.invalid('Agent ID is required');
    }

    final trimmedId = agentId.trim();

    // Check length constraints
    if (trimmedId.length < 3) {
      return ValidationResult.invalid('Agent ID must be at least 3 characters long');
    }

    if (trimmedId.length > 50) {
      return ValidationResult.invalid('Agent ID is too long (max 50 characters)');
    }

    // Check format
    if (!_agentIdPattern.hasMatch(trimmedId)) {
      return ValidationResult.invalid('Agent ID contains invalid characters. Only alphanumeric, hyphens, and underscores are allowed');
    }

    // Check for reserved words
    final lowerAgentId = trimmedId.toLowerCase();
    const reservedWords = ['admin', 'root', 'system', 'null', 'undefined'];
    if (reservedWords.contains(lowerAgentId)) {
      return ValidationResult.invalid('Agent ID cannot be a reserved word');
    }

    return ValidationResult.valid(trimmedId);
  }

  /// Validate and sanitize message content
  static ValidationResult validateMessage(String? message) {
    if (message == null || message.isEmpty) {
      return ValidationResult.invalid('Message cannot be empty');
    }

    final trimmedMessage = message.trim();

    // Check length constraints
    if (trimmedMessage.length < _minMessageLength) {
      return ValidationResult.invalid('Message is too short');
    }

    if (trimmedMessage.length > _maxMessageLength) {
      return ValidationResult.invalid('Message is too long (max $_maxMessageLength characters)');
    }

    // Check for dangerous patterns
    for (final pattern in _dangerousPatterns) {
      if (pattern.hasMatch(trimmedMessage)) {
        return ValidationResult.invalid('Message contains potentially dangerous content');
      }
    }

    // Check for SQL injection attempts
    for (final pattern in _sqlInjectionPatterns) {
      if (pattern.hasMatch(trimmedMessage)) {
        return ValidationResult.invalid('Message contains potentially malicious SQL patterns');
      }
    }

    // Sanitize the message
    final sanitizedMessage = _sanitizeMessage(trimmedMessage);

    return ValidationResult.valid(sanitizedMessage);
  }

  /// Validate URL format
  static ValidationResult validateUrl(String? url) {
    if (url == null || url.isEmpty) {
      return ValidationResult.invalid('URL is required');
    }

    final trimmedUrl = url.trim();

    // Parse URL
    final uri = Uri.tryParse(trimmedUrl);
    if (uri == null) {
      return ValidationResult.invalid('Invalid URL format');
    }

    // Check scheme
    if (!uri.hasScheme) {
      return ValidationResult.invalid('URL must include a scheme (http or https)');
    }

    if (uri.scheme != 'http' && uri.scheme != 'https') {
      return ValidationResult.invalid('URL must use HTTP or HTTPS protocol');
    }

    // Check authority
    if (!uri.hasAuthority) {
      return ValidationResult.invalid('URL must include a valid domain');
    }

    // Security check: prevent localhost and private IPs in production
    final host = uri.host.toLowerCase();
    if (host == 'localhost' || host == '127.0.0.1' || host.startsWith('192.168.') || host.startsWith('10.')) {
      return ValidationResult.invalid('Local and private network URLs are not allowed');
    }

    return ValidationResult.valid(trimmedUrl);
  }

  /// Sanitize message content
  static String _sanitizeMessage(String message) {
    // Remove null bytes
    String sanitized = message.replaceAll('\x00', '');

    // Normalize whitespace
    sanitized = sanitized.replaceAll(RegExp(r'\s+'), ' ');

    // Remove control characters except newlines and tabs
    sanitized = sanitized.replaceAll(RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]'), '');

    // Encode HTML entities to prevent XSS
    sanitized = _htmlEscape(sanitized);

    return sanitized.trim();
  }

  /// HTML escape for basic XSS prevention
  static String _htmlEscape(String text) {
    return text
        .replaceAll('&', '&amp;')
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;')
        .replaceAll('"', '&quot;')
        .replaceAll("'", '&#x27;');
  }

  /// Validate JSON structure for API requests
  static ValidationResult validateJsonStructure(Map<String, dynamic> json) {
    try {
      // Check for required fields based on API specification
      if (!json.containsKey('agent_id')) {
        return ValidationResult.invalid('Missing required field: agent_id');
      }

      // Validate agent_id in JSON
      final agentIdResult = validateAgentId(json['agent_id']?.toString());
      if (!agentIdResult.isValid) {
        return ValidationResult.invalid('Invalid agent_id: ${agentIdResult.error}');
      }

      // If question is present, validate it
      if (json.containsKey('question')) {
        final messageResult = validateMessage(json['question']?.toString());
        if (!messageResult.isValid) {
          return ValidationResult.invalid('Invalid question: ${messageResult.error}');
        }
      }

      // Validate session_id if present
      if (json.containsKey('session_id')) {
        final sessionId = json['session_id']?.toString();
        if (sessionId != null && sessionId.isNotEmpty) {
          if (sessionId.length < 3 || sessionId.length > 100) {
            return ValidationResult.invalid('Invalid session_id length');
          }
        }
      }

      // Create sanitized version
      final sanitizedJson = Map<String, dynamic>.from(json);
      if (json.containsKey('question')) {
        final messageResult = validateMessage(json['question']?.toString());
        sanitizedJson['question'] = messageResult.sanitizedValue;
      }

      return ValidationResult.valid(jsonEncode(sanitizedJson));
    } catch (e) {
      return ValidationResult.invalid('Invalid JSON structure: $e');
    }
  }

  /// Rate limiting validation (basic implementation)
  static final Map<String, List<DateTime>> _requestHistory = {};
  static const int _maxRequestsPerMinute = 60;

  static ValidationResult validateRateLimit(String identifier) {
    final now = DateTime.now();
    final oneMinuteAgo = now.subtract(const Duration(minutes: 1));

    // Clean old entries
    _requestHistory[identifier]?.removeWhere((time) => time.isBefore(oneMinuteAgo));

    // Get current request count
    final currentRequests = _requestHistory[identifier]?.length ?? 0;

    if (currentRequests >= _maxRequestsPerMinute) {
      return ValidationResult.invalid('Rate limit exceeded. Maximum $_maxRequestsPerMinute requests per minute.');
    }

    // Add current request
    _requestHistory[identifier] ??= [];
    _requestHistory[identifier]!.add(now);

    return ValidationResult.valid('Rate limit check passed');
  }
}
