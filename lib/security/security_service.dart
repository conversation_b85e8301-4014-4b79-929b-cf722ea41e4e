import '../config/chat_config.dart';
import 'input_validator.dart';

/// Security exception for validation failures
class SecurityException implements Exception {
  final String message;
  final String? details;

  SecurityException(this.message, {this.details});

  @override
  String toString() => 'SecurityException: $message${details != null ? ' ($details)' : ''}';
}

/// Security service for chat application
class SecurityService {
  // Private constructor to prevent instantiation
  SecurityService._();

  /// Validate configuration security
  static void validateConfiguration() {
    final configResult = ChatConfig.validateConfiguration();
    
    if (!configResult.isValid) {
      throw SecurityException(
        'Configuration validation failed',
        details: configResult.errors.join(', '),
      );
    }

    // Additional security checks
    if (ChatConfig.apiKey.isEmpty) {
      throw SecurityException('API key is not configured');
    }

    if (ChatConfig.baseUrl.isEmpty) {
      throw SecurityException('Base URL is not configured');
    }

    // Validate API key format
    final apiKeyResult = InputValidator.validateApiKey(ChatConfig.apiKey);
    if (!apiKeyResult.isValid) {
      throw SecurityException(
        'Invalid API key configuration',
        details: apiKeyResult.error,
      );
    }

    // Validate agent ID format
    final agentIdResult = InputValidator.validateAgentId(ChatConfig.defaultAgentId);
    if (!agentIdResult.isValid) {
      throw SecurityException(
        'Invalid agent ID configuration',
        details: agentIdResult.error,
      );
    }

    // Validate base URL
    final urlResult = InputValidator.validateUrl(ChatConfig.baseUrl);
    if (!urlResult.isValid) {
      throw SecurityException(
        'Invalid base URL configuration',
        details: urlResult.error,
      );
    }
  }

  /// Validate and sanitize API request parameters
  static Map<String, dynamic> validateAndSanitizeRequest({
    required String agentId,
    String? message,
    String? sessionId,
  }) {
    // Validate agent ID
    final agentIdResult = InputValidator.validateAgentId(agentId);
    if (!agentIdResult.isValid) {
      throw SecurityException(
        'Invalid agent ID',
        details: agentIdResult.error,
      );
    }

    final sanitizedRequest = <String, dynamic>{
      'agent_id': agentIdResult.sanitizedValue,
      'stream': false, // Always false for security
    };

    // Validate and sanitize message if provided
    if (message != null) {
      final messageResult = InputValidator.validateMessage(message);
      if (!messageResult.isValid) {
        throw SecurityException(
          'Invalid message content',
          details: messageResult.error,
        );
      }
      sanitizedRequest['question'] = messageResult.sanitizedValue;
    }

    // Validate session ID if provided
    if (sessionId != null && sessionId.isNotEmpty) {
      if (sessionId.length < 3 || sessionId.length > 100) {
        throw SecurityException('Invalid session ID format');
      }
      
      // Basic session ID format validation
      if (!RegExp(r'^[a-zA-Z0-9\-_]{3,100}$').hasMatch(sessionId)) {
        throw SecurityException('Session ID contains invalid characters');
      }
      
      sanitizedRequest['session_id'] = sessionId;
    }

    return sanitizedRequest;
  }

  /// Validate API response for security issues
  static void validateApiResponse(Map<String, dynamic> response) {
    // Check for required fields
    if (!response.containsKey('code')) {
      throw SecurityException('API response missing status code');
    }

    final code = response['code'];
    if (code is! int) {
      throw SecurityException('Invalid status code format');
    }

    // Check for suspicious response patterns
    final responseString = response.toString().toLowerCase();
    
    // Check for potential script injection in response
    if (responseString.contains('<script') || 
        responseString.contains('javascript:') ||
        responseString.contains('vbscript:')) {
      throw SecurityException('API response contains potentially malicious content');
    }

    // Validate data structure if present
    if (response.containsKey('data') && response['data'] != null) {
      final data = response['data'];
      if (data is Map<String, dynamic>) {
        _validateResponseData(data);
      }
    }
  }

  /// Validate response data structure
  static void _validateResponseData(Map<String, dynamic> data) {
    // Validate answer field if present
    if (data.containsKey('answer')) {
      final answer = data['answer'];
      if (answer is String && answer.isNotEmpty) {
        // Check answer length
        if (answer.length > 50000) {
          throw SecurityException('API response answer is too long');
        }
        
        // Check for suspicious patterns
        if (answer.contains('<script') || answer.contains('javascript:')) {
          throw SecurityException('API response contains potentially malicious content');
        }
      }
    }

    // Validate session ID if present
    if (data.containsKey('session_id')) {
      final sessionId = data['session_id'];
      if (sessionId is String && sessionId.isNotEmpty) {
        if (sessionId.length > 100) {
          throw SecurityException('Session ID in response is too long');
        }
      }
    }

    // Validate reference data if present
    if (data.containsKey('reference') && data['reference'] != null) {
      final reference = data['reference'];
      if (reference is Map) {
        _validateReferenceData(reference);
      }
    }
  }

  /// Validate reference data for security issues
  static void _validateReferenceData(Map reference) {
    for (final entry in reference.entries) {
      final key = entry.key.toString();
      final value = entry.value.toString();
      
      // Check key length
      if (key.length > 100) {
        throw SecurityException('Reference key is too long');
      }
      
      // Check value length
      if (value.length > 1000) {
        throw SecurityException('Reference value is too long');
      }
      
      // Check for suspicious patterns
      if (value.contains('<script') || value.contains('javascript:')) {
        throw SecurityException('Reference data contains potentially malicious content');
      }
    }
  }

  /// Check rate limiting for requests
  static void checkRateLimit(String identifier) {
    final rateLimitResult = InputValidator.validateRateLimit(identifier);
    if (!rateLimitResult.isValid) {
      throw SecurityException(
        'Rate limit exceeded',
        details: rateLimitResult.error,
      );
    }
  }

  /// Sanitize text content for display
  static String sanitizeForDisplay(String content) {
    // Remove null bytes
    String sanitized = content.replaceAll('\x00', '');
    
    // Normalize line endings
    sanitized = sanitized.replaceAll('\r\n', '\n').replaceAll('\r', '\n');
    
    // Remove excessive whitespace but preserve intentional formatting
    sanitized = sanitized.replaceAll(RegExp(r'\n{3,}'), '\n\n');
    
    // Remove control characters except newlines and tabs
    sanitized = sanitized.replaceAll(RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]'), '');
    
    return sanitized;
  }

  /// Generate secure headers for API requests
  static Map<String, String> generateSecureHeaders({
    required String apiKey,
    String? userAgent,
  }) {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': userAgent ?? 'ChatApp/1.0',
    };

    // Add authorization header
    if (apiKey.startsWith('Bearer ')) {
      headers['Authorization'] = apiKey;
    } else {
      headers['Authorization'] = 'Bearer $apiKey';
    }

    // Add security headers
    headers['X-Requested-With'] = 'XMLHttpRequest';
    headers['Cache-Control'] = 'no-cache, no-store, must-revalidate';
    headers['Pragma'] = 'no-cache';

    return headers;
  }

  /// Validate SSL certificate (placeholder for future implementation)
  static bool validateSslCertificate(String host) {
    // This would be implemented with actual certificate validation
    // For now, return true if SSL validation is enabled in config
    return ChatConfig.validateSslCertificates;
  }

  /// Log security events (placeholder for future implementation)
  static void logSecurityEvent({
    required String event,
    required String level, // 'info', 'warning', 'error'
    Map<String, dynamic>? details,
  }) {
    if (ChatConfig.enableLogging) {
      final timestamp = DateTime.now().toIso8601String();
      final logEntry = {
        'timestamp': timestamp,
        'event': event,
        'level': level,
        'details': details,
      };
      
      // In a real implementation, this would write to a secure log file
      // or send to a logging service
      if (ChatConfig.isDebugMode) {
        print('SECURITY_LOG: $logEntry');
      }
    }
  }

  /// Clear sensitive data from memory (placeholder)
  static void clearSensitiveData() {
    // In a real implementation, this would clear sensitive data from memory
    // This is a placeholder for future security enhancements
    InputValidator._requestHistory.clear();
  }
}
