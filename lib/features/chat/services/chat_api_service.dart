import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/api_models.dart';
import '../../../config/chat_config.dart';

/// Exception thrown when API request fails
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final String? responseBody;

  ApiException(this.message, {this.statusCode, this.responseBody});

  @override
  String toString() => 'ApiException: $message';
}

/// Exception thrown when network request times out
class TimeoutException extends ApiException {
  TimeoutException() : super('Request timed out');
}

/// Exception thrown when there's no internet connection
class NetworkException extends ApiException {
  NetworkException() : super('No internet connection');
}

/// Exception thrown when authentication fails
class AuthenticationException extends ApiException {
  AuthenticationException() : super('Authentication failed - invalid API key');
}

/// Exception thrown when agent ID is invalid
class InvalidAgentException extends ApiException {
  InvalidAgentException() : super('Invalid agent ID');
}

/// Service class for handling HTTP communication with the AI chat API
class ChatApiService {
  final http.Client _client;
  final String _baseUrl;
  final String _apiKey;

  ChatApiService({
    http.Client? client,
    String? baseUrl,
    String? apiKey,
  })  : _client = client ?? http.Client(),
        _baseUrl = baseUrl ?? ChatConfig.baseUrl,
        _apiKey = apiKey ?? ChatConfig.apiKey;

  /// Get default headers for API requests
  Map<String, String> get _headers => {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_apiKey',
        'Accept': 'application/json',
      };

  /// Initialize a new chat session with the AI agent
  Future<CompletionResponse> initializeSession(String agentId) async {
    final request = CompletionRequest(
      stream: false,
      sessionId: null, // No session ID for initialization
    );

    return _makeRequest(
      endpoint: '/completions',
      body: request.toJson(),
      agentId: agentId,
    );
  }

  /// Send a message to the AI agent
  Future<CompletionResponse> sendMessage({
    required String agentId,
    required String message,
    required String sessionId,
  }) async {
    final request = CompletionRequest(
      question: message,
      stream: false,
      sessionId: sessionId,
    );

    return _makeRequest(
      endpoint: '/completions',
      body: request.toJson(),
      agentId: agentId,
    );
  }

  /// Make HTTP POST request to the API
  Future<CompletionResponse> _makeRequest({
    required String endpoint,
    required Map<String, dynamic> body,
    required String agentId,
  }) async {
    final url = Uri.parse('$_baseUrl$endpoint');

    // Add agent_id to the request body
    final requestBody = Map<String, dynamic>.from(body);
    requestBody['agent_id'] = agentId;

    try {
      final response = await _client
          .post(
            url,
            headers: _headers,
            body: json.encode(requestBody),
          )
          .timeout(
            Duration(seconds: ChatConfig.requestTimeoutSeconds),
            onTimeout: () => throw TimeoutException(),
          );

      return _handleResponse(response);
    } on SocketException {
      throw NetworkException();
    } on TimeoutException {
      rethrow;
    } on AuthenticationException {
      rethrow;
    } on InvalidAgentException {
      rethrow;
    } on ApiException {
      rethrow;
    } catch (e) {
      throw ApiException('Unexpected error: $e');
    }
  }

  /// Handle HTTP response and convert to CompletionResponse
  CompletionResponse _handleResponse(http.Response response) {
    final statusCode = response.statusCode;
    final responseBody = response.body;

    // Handle different HTTP status codes
    switch (statusCode) {
      case 200:
        return _parseSuccessResponse(responseBody);
      case 401:
        throw AuthenticationException();
      case 400:
        throw InvalidAgentException();
      case 404:
        throw ApiException('API endpoint not found', statusCode: statusCode);
      case 429:
        throw ApiException('Rate limit exceeded', statusCode: statusCode);
      case 500:
      case 502:
      case 503:
      case 504:
        throw ApiException('Server error', statusCode: statusCode);
      default:
        throw ApiException(
          'HTTP error: $statusCode',
          statusCode: statusCode,
          responseBody: responseBody,
        );
    }
  }

  /// Parse successful API response
  CompletionResponse _parseSuccessResponse(String responseBody) {
    try {
      final jsonData = json.decode(responseBody) as Map<String, dynamic>;
      return CompletionResponse.fromJson(jsonData);
    } catch (e) {
      throw ApiException('Failed to parse response: $e');
    }
  }

  /// Dispose of resources
  void dispose() {
    _client.close();
  }
}
