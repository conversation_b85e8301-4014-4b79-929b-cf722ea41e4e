import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

/// Service for monitoring network connectivity
class ConnectivityService extends ChangeNotifier {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  bool _isConnected = true;
  Timer? _connectivityTimer;
  final Duration _checkInterval = const Duration(seconds: 5);

  bool get isConnected => _isConnected;

  /// Start monitoring connectivity
  void startMonitoring() {
    _connectivityTimer?.cancel();
    _connectivityTimer =
        Timer.periodic(_checkInterval, (_) => _checkConnectivity());
    _checkConnectivity(); // Initial check
  }

  /// Stop monitoring connectivity
  void stopMonitoring() {
    _connectivityTimer?.cancel();
    _connectivityTimer = null;
  }

  /// Check current connectivity status
  Future<bool> checkConnectivity() async {
    return await _checkConnectivity();
  }

  /// Internal connectivity check
  Future<bool> _checkConnectivity() async {
    try {
      final result = await InternetAddress.lookup('google.com')
          .timeout(const Duration(seconds: 3));

      final wasConnected = _isConnected;
      _isConnected = result.isNotEmpty && result[0].rawAddress.isNotEmpty;

      // Notify listeners only if status changed
      if (wasConnected != _isConnected) {
        notifyListeners();
      }

      return _isConnected;
    } catch (e) {
      final wasConnected = _isConnected;
      _isConnected = false;

      // Notify listeners only if status changed
      if (wasConnected != _isConnected) {
        notifyListeners();
      }

      return false;
    }
  }

  @override
  void dispose() {
    stopMonitoring();
    super.dispose();
  }
}

/// Widget that provides connectivity status to its children
class ConnectivityProvider extends StatefulWidget {
  final Widget child;

  const ConnectivityProvider({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<ConnectivityProvider> createState() => _ConnectivityProviderState();
}

class _ConnectivityProviderState extends State<ConnectivityProvider> {
  final ConnectivityService _connectivityService = ConnectivityService();

  @override
  void initState() {
    super.initState();
    _connectivityService.startMonitoring();
  }

  @override
  void dispose() {
    _connectivityService.stopMonitoring();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<ConnectivityService>.value(
      value: _connectivityService,
      child: widget.child,
    );
  }
}

/// Widget that shows connectivity status
class ConnectivityIndicator extends StatelessWidget {
  final Widget? child;
  final bool showWhenConnected;

  const ConnectivityIndicator({
    Key? key,
    this.child,
    this.showWhenConnected = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<ConnectivityService>(
      builder: (context, connectivity, child) {
        if (connectivity.isConnected && !showWhenConnected) {
          return child ?? const SizedBox.shrink();
        }

        final theme = Theme.of(context);
        final isConnected = connectivity.isConnected;

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          color: isConnected
              ? Colors.green.withValues(alpha: 0.1)
              : Colors.red.withValues(alpha: 0.1),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                isConnected ? Icons.wifi : Icons.wifi_off,
                size: 16,
                color: isConnected ? Colors.green : Colors.red,
              ),
              const SizedBox(width: 8),
              Text(
                isConnected ? 'Connected' : 'No internet connection',
                style: theme.textTheme.bodySmall?.copyWith(
                  color:
                      isConnected ? Colors.green.shade700 : Colors.red.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );
      },
      child: child,
    );
  }
}
