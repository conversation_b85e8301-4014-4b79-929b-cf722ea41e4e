import 'message.dart';

class ChatSession {
  final String sessionId;
  final String agentId;
  final DateTime createdAt;
  final List<Message> messages;

  ChatSession({
    required this.sessionId,
    required this.agentId,
    DateTime? createdAt,
    List<Message>? messages,
  })  : createdAt = createdAt ?? DateTime.now(),
        messages = messages ?? [];

  Map<String, dynamic> toJson() {
    return {
      'sessionId': sessionId,
      'agentId': agentId,
      'createdAt': createdAt.toIso8601String(),
      'messages': messages.map((message) => message.toJson()).toList(),
    };
  }

  factory ChatSession.fromJson(Map<String, dynamic> json) {
    return ChatSession(
      sessionId: json['sessionId'] as String,
      agentId: json['agentId'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      messages: (json['messages'] as List<dynamic>?)
              ?.map((messageJson) =>
                  Message.fromJson(messageJson as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  ChatSession copyWith({
    String? sessionId,
    String? agentId,
    DateTime? createdAt,
    List<Message>? messages,
  }) {
    return ChatSession(
      sessionId: sessionId ?? this.sessionId,
      agentId: agentId ?? this.agentId,
      createdAt: createdAt ?? this.createdAt,
      messages: messages ?? List.from(this.messages),
    );
  }

  ChatSession addMessage(Message message) {
    return copyWith(
      messages: [...messages, message],
    );
  }

  ChatSession updateMessage(String messageId, Message updatedMessage) {
    final updatedMessages = messages.map((message) {
      return message.id == messageId ? updatedMessage : message;
    }).toList();

    return copyWith(messages: updatedMessages);
  }

  ChatSession removeMessage(String messageId) {
    final filteredMessages =
        messages.where((message) => message.id != messageId).toList();
    return copyWith(messages: filteredMessages);
  }

  Message? getMessageById(String messageId) {
    try {
      return messages.firstWhere((message) => message.id == messageId);
    } catch (e) {
      return null;
    }
  }

  List<Message> getMessagesByType(MessageType type) {
    return messages.where((message) => message.type == type).toList();
  }

  List<Message> getPendingMessages() {
    return messages
        .where((message) => message.status == MessageStatus.sending)
        .toList();
  }

  bool get hasMessages => messages.isNotEmpty;
  bool get hasPendingMessages => getPendingMessages().isNotEmpty;
  int get messageCount => messages.length;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatSession &&
        other.sessionId == sessionId &&
        other.agentId == agentId &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode => Object.hash(sessionId, agentId, createdAt);

  @override
  String toString() {
    return 'ChatSession(sessionId: $sessionId, agentId: $agentId, createdAt: $createdAt, messageCount: $messageCount)';
  }
}
