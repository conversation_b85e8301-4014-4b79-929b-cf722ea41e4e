class CompletionRequest {
  final String? question;
  final bool stream;
  final String? sessionId;

  CompletionRequest({
    this.question,
    this.stream = false,
    this.sessionId,
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'stream': stream,
    };

    if (question != null) {
      json['question'] = question;
    }

    if (sessionId != null) {
      json['session_id'] = sessionId;
    }

    return json;
  }

  factory CompletionRequest.fromJson(Map<String, dynamic> json) {
    return CompletionRequest(
      question: json['question'] as String?,
      stream: json['stream'] as bool? ?? false,
      sessionId: json['session_id'] as String?,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CompletionRequest &&
        other.question == question &&
        other.stream == stream &&
        other.sessionId == sessionId;
  }

  @override
  int get hashCode => Object.hash(question, stream, sessionId);

  @override
  String toString() {
    return 'CompletionRequest(question: $question, stream: $stream, sessionId: $sessionId)';
  }
}

class CompletionData {
  final String answer;
  final String id;
  final String sessionId;
  final Map<String, dynamic> reference;
  final List<dynamic> param;

  CompletionData({
    required this.answer,
    required this.id,
    required this.sessionId,
    required this.reference,
    required this.param,
  });

  factory CompletionData.fromJson(Map<String, dynamic> json) {
    final referenceJson = json['reference'];
    Map<String, dynamic> referenceMap = {};

    if (referenceJson is Map<String, dynamic>) {
      referenceMap = referenceJson;
    } else if (referenceJson is Map) {
      referenceMap = Map<String, dynamic>.from(referenceJson);
    }

    return CompletionData(
      answer: json['answer'] as String? ?? '',
      id: json['id'] as String? ?? '',
      sessionId: json['session_id'] as String? ?? '',
      reference: referenceMap,
      param: json['param'] as List<dynamic>? ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'answer': answer,
      'id': id,
      'session_id': sessionId,
      'reference': reference,
      'param': param,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CompletionData &&
        other.answer == answer &&
        other.id == id &&
        other.sessionId == sessionId;
  }

  @override
  int get hashCode => Object.hash(answer, id, sessionId);

  @override
  String toString() {
    return 'CompletionData(answer: $answer, id: $id, sessionId: $sessionId)';
  }
}

class CompletionResponse {
  final int code;
  final String message;
  final CompletionData data;

  CompletionResponse({
    required this.code,
    required this.message,
    required this.data,
  });

  factory CompletionResponse.fromJson(Map<String, dynamic> json) {
    final dataJson = json['data'];
    Map<String, dynamic> dataMap = {};

    if (dataJson is Map<String, dynamic>) {
      dataMap = dataJson;
    } else if (dataJson is Map) {
      dataMap = Map<String, dynamic>.from(dataJson);
    }

    return CompletionResponse(
      code: json['code'] as int? ?? 0,
      message: json['message'] as String? ?? '',
      data: CompletionData.fromJson(dataMap),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data.toJson(),
    };
  }

  bool get isSuccess => code == 200;
  bool get isError => code != 200;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CompletionResponse &&
        other.code == code &&
        other.message == message &&
        other.data == data;
  }

  @override
  int get hashCode => Object.hash(code, message, data);

  @override
  String toString() {
    return 'CompletionResponse(code: $code, message: $message, data: $data)';
  }
}
