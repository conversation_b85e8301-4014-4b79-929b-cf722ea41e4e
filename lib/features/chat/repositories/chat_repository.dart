import '../models/api_models.dart';
import '../services/chat_api_service.dart';
import '../../../config/chat_config.dart';

/// Abstract repository interface for chat operations
abstract class ChatRepository {
  /// Initialize a new chat session with the AI agent
  /// Returns a CompletionResponse containing the welcome message and session ID
  Future<CompletionResponse> initializeSession(String agentId);

  /// Send a message to the AI agent within an existing session
  /// Returns a CompletionResponse containing the AI's response
  Future<CompletionResponse> sendMessage({
    required String agentId,
    required String message,
    required String sessionId,
  });

  /// End the current chat session (optional cleanup)
  Future<void> endSession(String sessionId);

  /// Dispose of any resources used by the repository
  void dispose();
}

/// Implementation of ChatRepository using the API service
class ApiChatRepository implements ChatRepository {
  final ChatApiService _apiService;

  ApiChatRepository({ChatApiService? apiService})
      : _apiService = apiService ?? ChatApiService();

  @override
  Future<CompletionResponse> initializeSession(String agentId) async {
    return await _executeWithRetry(
      () => _apiService.initializeSession(agentId),
      operationName: 'initialize session',
    );
  }

  @override
  Future<CompletionResponse> sendMessage({
    required String agentId,
    required String message,
    required String sessionId,
  }) async {
    return await _executeWithRetry(
      () => _apiService.sendMessage(
        agentId: agentId,
        message: message,
        sessionId: sessionId,
      ),
      operationName: 'send message',
    );
  }

  @override
  Future<void> endSession(String sessionId) async {
    // Currently, the API doesn't have an explicit end session endpoint
    // This method is a placeholder for future implementation
    // For now, we just log that the session is ending
    // In a real implementation, this might clear local session data
  }

  @override
  void dispose() {
    _apiService.dispose();
  }

  /// Execute an API operation with retry logic
  Future<CompletionResponse> _executeWithRetry(
    Future<CompletionResponse> Function() operation, {
    required String operationName,
    int maxRetries = ChatConfig.maxRetries,
  }) async {
    ApiException? lastException;

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } on AuthenticationException {
        // Don't retry authentication errors
        rethrow;
      } on InvalidAgentException {
        // Don't retry invalid agent errors
        rethrow;
      } on NetworkException catch (e) {
        lastException = e;
        if (attempt < maxRetries) {
          // Wait before retrying network errors
          await Future.delayed(ChatConfig.retryDelay);
        }
      } on TimeoutException catch (e) {
        lastException = e;
        if (attempt < maxRetries) {
          // Wait before retrying timeout errors
          await Future.delayed(ChatConfig.retryDelay);
        }
      } on ApiException catch (e) {
        // Check if it's a server error that might be retryable
        if (e.statusCode != null && _isRetryableStatusCode(e.statusCode!)) {
          lastException = e;
          if (attempt < maxRetries) {
            // Wait before retrying server errors
            await Future.delayed(ChatConfig.retryDelay);
          }
        } else {
          // Don't retry client errors (4xx except 429)
          rethrow;
        }
      } catch (e) {
        // Handle any unexpected exceptions
        lastException = ApiException('Unexpected error: $e');
        if (attempt < maxRetries) {
          await Future.delayed(ChatConfig.retryDelay);
        }
      }
    }

    // If we've exhausted all retries, throw the last exception
    throw lastException ??
        ApiException('Failed to $operationName after $maxRetries attempts');
  }

  /// Check if an HTTP status code is retryable
  bool _isRetryableStatusCode(int statusCode) {
    return statusCode == 429 || // Rate limit
        statusCode >= 500; // Server errors
  }
}
