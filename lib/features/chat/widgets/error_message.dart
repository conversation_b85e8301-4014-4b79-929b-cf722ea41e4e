import 'package:flutter/material.dart';

/// Types of errors that can occur in the chat system
enum ChatErrorType {
  network,
  authentication,
  validation,
  server,
  timeout,
  unknown,
}

/// Error information container
class ChatError {
  final ChatErrorType type;
  final String message;
  final String? details;
  final bool isRetryable;
  final VoidCallback? onRetry;

  const ChatError({
    required this.type,
    required this.message,
    this.details,
    this.isRetryable = false,
    this.onRetry,
  });

  /// Create error from exception
  factory ChatError.fromException(Exception exception,
      {VoidCallback? onRetry}) {
    final message = exception.toString();

    if (message.contains('SocketException') ||
        message.contains('HandshakeException') ||
        message.contains('Connection') ||
        message.contains('network')) {
      return ChatError(
        type: ChatErrorType.network,
        message: 'Network connection failed',
        details: 'Please check your internet connection and try again.',
        isRetryable: true,
        onRetry: onRetry,
      );
    }

    if (message.contains('TimeoutException') || message.contains('timeout')) {
      return ChatError(
        type: ChatErrorType.timeout,
        message: 'Request timed out',
        details: 'The request took too long to complete. Please try again.',
        isRetryable: true,
        onRetry: onRetry,
      );
    }

    if (message.contains('401') ||
        message.contains('Unauthorized') ||
        message.contains('authentication')) {
      return ChatError(
        type: ChatErrorType.authentication,
        message: 'Authentication failed',
        details:
            'Invalid API key or credentials. Please check your configuration.',
        isRetryable: false,
      );
    }

    if (message.contains('400') ||
        message.contains('validation') ||
        message.contains('Invalid agent')) {
      return ChatError(
        type: ChatErrorType.validation,
        message: 'Invalid request',
        details: 'The request contains invalid data. Please try again.',
        isRetryable: false,
      );
    }

    if (message.contains('500') ||
        message.contains('502') ||
        message.contains('503') ||
        message.contains('server')) {
      return ChatError(
        type: ChatErrorType.server,
        message: 'Server error',
        details: 'The server is experiencing issues. Please try again later.',
        isRetryable: true,
        onRetry: onRetry,
      );
    }

    return ChatError(
      type: ChatErrorType.unknown,
      message: 'An unexpected error occurred',
      details: message,
      isRetryable: true,
      onRetry: onRetry,
    );
  }

  /// Get appropriate icon for error type
  IconData get icon {
    switch (type) {
      case ChatErrorType.network:
        return Icons.wifi_off;
      case ChatErrorType.authentication:
        return Icons.lock;
      case ChatErrorType.validation:
        return Icons.warning;
      case ChatErrorType.server:
        return Icons.dns;
      case ChatErrorType.timeout:
        return Icons.timer_off;
      case ChatErrorType.unknown:
        return Icons.error;
    }
  }

  /// Get color for error type
  Color getColor(ColorScheme colorScheme) {
    switch (type) {
      case ChatErrorType.network:
        return Colors.orange;
      case ChatErrorType.authentication:
        return Colors.red;
      case ChatErrorType.validation:
        return Colors.amber;
      case ChatErrorType.server:
        return colorScheme.error;
      case ChatErrorType.timeout:
        return Colors.blue;
      case ChatErrorType.unknown:
        return colorScheme.error;
    }
  }
}

/// Widget for displaying error messages with appropriate styling and actions
class ErrorMessage extends StatelessWidget {
  final ChatError error;
  final VoidCallback? onDismiss;
  final bool showDetails;
  final EdgeInsets? padding;

  const ErrorMessage({
    Key? key,
    required this.error,
    this.onDismiss,
    this.showDetails = true,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final errorColor = error.getColor(colorScheme);

    return Container(
      width: double.infinity,
      padding: padding ?? const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: errorColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: errorColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                error.icon,
                color: errorColor,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      error.message,
                      style: theme.textTheme.titleSmall?.copyWith(
                        color: errorColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (showDetails && error.details != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        error.details!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              if (onDismiss != null)
                IconButton(
                  onPressed: onDismiss,
                  icon: Icon(
                    Icons.close,
                    size: 18,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 24,
                    minHeight: 24,
                  ),
                ),
            ],
          ),
          if (error.isRetryable && error.onRetry != null) ...[
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: error.onRetry,
                  icon: Icon(
                    Icons.refresh,
                    size: 16,
                    color: errorColor,
                  ),
                  label: Text(
                    'Retry',
                    style: TextStyle(color: errorColor),
                  ),
                  style: TextButton.styleFrom(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}

/// Inline error widget for failed messages
class InlineErrorMessage extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;
  final bool isCompact;

  const InlineErrorMessage({
    Key? key,
    required this.message,
    this.onRetry,
    this.isCompact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final errorColor = theme.colorScheme.error;

    return Container(
      padding: EdgeInsets.all(isCompact ? 8 : 12),
      decoration: BoxDecoration(
        color: theme.colorScheme.errorContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: errorColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(
                Icons.error_outline,
                color: errorColor,
                size: isCompact ? 16 : 18,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  message,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onErrorContainer,
                    fontSize: isCompact ? 12 : 13,
                  ),
                ),
              ),
            ],
          ),
          if (onRetry != null) ...[
            const SizedBox(height: 8),
            Align(
              alignment: Alignment.centerRight,
              child: TextButton.icon(
                onPressed: onRetry,
                icon: Icon(
                  Icons.refresh,
                  size: 14,
                  color: errorColor,
                ),
                label: Text(
                  'Retry',
                  style: TextStyle(
                    color: errorColor,
                    fontSize: 12,
                  ),
                ),
                style: TextButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Banner error widget for persistent errors
class ErrorBanner extends StatelessWidget {
  final ChatError error;
  final VoidCallback? onDismiss;

  const ErrorBanner({
    Key? key,
    required this.error,
    this.onDismiss,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final errorColor = error.getColor(theme.colorScheme);

    return Material(
      color: errorColor.withValues(alpha: 0.1),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: errorColor.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            Icon(
              error.icon,
              color: errorColor,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    error.message,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: errorColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (error.details != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      error.details!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color:
                            theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            if (error.isRetryable && error.onRetry != null) ...[
              TextButton(
                onPressed: error.onRetry,
                child: Text(
                  'Retry',
                  style: TextStyle(color: errorColor),
                ),
                style: TextButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
              const SizedBox(width: 8),
            ],
            if (onDismiss != null)
              IconButton(
                onPressed: onDismiss,
                icon: Icon(
                  Icons.close,
                  size: 18,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(
                  minWidth: 32,
                  minHeight: 32,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
