import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../config/chat_config.dart';

/// Widget for composing and sending chat messages
class ChatInput extends StatefulWidget {
  final Function(String) onSendMessage;
  final bool isEnabled;
  final bool isLoading;
  final String? placeholder;
  final int? maxLines;
  final int? minLines;

  const ChatInput({
    Key? key,
    required this.onSendMessage,
    this.isEnabled = true,
    this.isLoading = false,
    this.placeholder,
    this.maxLines = 6,
    this.minLines = 1,
  }) : super(key: key);

  @override
  State<ChatInput> createState() => _ChatInputState();
}

class _ChatInputState extends State<ChatInput> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = _controller.text.trim().isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
    }
  }

  void _sendMessage() {
    final text = _controller.text.trim();
    if (text.isNotEmpty && widget.isEnabled && !widget.isLoading) {
      widget.onSendMessage(text);
      _controller.clear();
      _focusNode.requestFocus();
    }
  }

  void _handleKeyboardEvent(KeyEvent event) {
    // Handle Ctrl/Cmd + Enter to send message
    if (event is KeyDownEvent) {
      final isEnterPressed = event.logicalKey == LogicalKeyboardKey.enter;
      final isControlPressed = HardwareKeyboard.instance.logicalKeysPressed
          .contains(LogicalKeyboardKey.controlLeft) ||
          HardwareKeyboard.instance.logicalKeysPressed
              .contains(LogicalKeyboardKey.controlRight) ||
          HardwareKeyboard.instance.logicalKeysPressed
              .contains(LogicalKeyboardKey.metaLeft) ||
          HardwareKeyboard.instance.logicalKeysPressed
              .contains(LogicalKeyboardKey.metaRight);

      if (isEnterPressed && isControlPressed) {
        _sendMessage();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(
            color: colorScheme.outline.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Expanded(
              child: KeyboardListener(
                focusNode: FocusNode(),
                onKeyEvent: _handleKeyboardEvent,
                child: Container(
                  constraints: const BoxConstraints(
                    minHeight: 40,
                  ),
                  decoration: BoxDecoration(
                    color: colorScheme.surface,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: _focusNode.hasFocus
                          ? colorScheme.primary.withOpacity(0.5)
                          : colorScheme.outline.withOpacity(0.3),
                      width: 1.5,
                    ),
                  ),
                  child: TextField(
                    controller: _controller,
                    focusNode: _focusNode,
                    enabled: widget.isEnabled && !widget.isLoading,
                    maxLines: widget.maxLines,
                    minLines: widget.minLines,
                    maxLength: ChatConfig.maxMessageLength,
                    textCapitalization: TextCapitalization.sentences,
                    keyboardType: TextInputType.multiline,
                    textInputAction: TextInputAction.newline,
                    style: theme.textTheme.bodyLarge,
                    decoration: InputDecoration(
                      hintText: widget.placeholder ?? 'Type your message...',
                      hintStyle: theme.textTheme.bodyLarge?.copyWith(
                        color: colorScheme.onSurface.withOpacity(0.5),
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      counterText: '', // Hide character counter
                    ),
                    onSubmitted: (_) {
                      // Handle Enter key submission (mobile)
                      if (!HardwareKeyboard.instance.logicalKeysPressed
                              .contains(LogicalKeyboardKey.shift) &&
                          _hasText) {
                        _sendMessage();
                      }
                    },
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            _buildSendButton(context, theme, colorScheme),
          ],
        ),
      ),
    );
  }

  Widget _buildSendButton(
    BuildContext context,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    final canSend = _hasText && widget.isEnabled && !widget.isLoading;

    return Container(
      width: 44,
      height: 44,
      child: widget.isLoading
          ? _buildLoadingButton(colorScheme)
          : _buildActionButton(context, colorScheme, canSend),
    );
  }

  Widget _buildLoadingButton(ColorScheme colorScheme) {
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.primary.withOpacity(0.1),
        shape: BoxShape.circle,
      ),
      child: Center(
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              colorScheme.primary.withOpacity(0.7),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    ColorScheme colorScheme,
    bool canSend,
  ) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      decoration: BoxDecoration(
        color: canSend
            ? colorScheme.primary
            : colorScheme.primary.withOpacity(0.1),
        shape: BoxShape.circle,
        boxShadow: canSend
            ? [
                BoxShadow(
                  offset: const Offset(0, 2),
                  blurRadius: 4,
                  color: colorScheme.primary.withOpacity(0.3),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: canSend ? _sendMessage : null,
          borderRadius: BorderRadius.circular(22),
          child: Center(
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: Icon(
                canSend ? Icons.send : Icons.send_outlined,
                key: ValueKey(canSend),
                color: canSend
                    ? colorScheme.onPrimary
                    : colorScheme.primary.withOpacity(0.5),
                size: 20,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Helper widget for showing input validation errors
class ChatInputError extends StatelessWidget {
  final String message;
  final VoidCallback? onDismiss;

  const ChatInputError({
    Key? key,
    required this.message,
    this.onDismiss,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colorScheme.error.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: colorScheme.onErrorContainer,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: theme.textTheme.bodySmall?.copyWith(
                color: colorScheme.onErrorContainer,
              ),
            ),
          ),
          if (onDismiss != null) ...[
            const SizedBox(width: 8),
            GestureDetector(
              onTap: onDismiss,
              child: Icon(
                Icons.close,
                color: colorScheme.onErrorContainer.withOpacity(0.7),
                size: 16,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Helper widget for showing typing indicators or hints
class ChatInputHint extends StatelessWidget {
  final String message;
  final IconData? icon;
  final Color? color;

  const ChatInputHint({
    Key? key,
    required this.message,
    this.icon,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveColor = color ?? theme.colorScheme.onSurface.withOpacity(0.6);

    return Padding(
      padding: const EdgeInsets.only(top: 8, left: 16),
      child: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              color: effectiveColor,
              size: 14,
            ),
            const SizedBox(width: 6),
          ],
          Text(
            message,
            style: theme.textTheme.bodySmall?.copyWith(
              color: effectiveColor,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}