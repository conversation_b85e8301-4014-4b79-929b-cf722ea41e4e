import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/message.dart';
import 'error_message.dart';

/// Widget for displaying individual chat messages in bubble format
class MessageBubble extends StatelessWidget {
  final Message message;
  final VoidCallback? onRetry;
  final bool showAvatar;
  final bool showTimestamp;

  const MessageBubble({
    Key? key,
    required this.message,
    this.onRetry,
    this.showAvatar = true,
    this.showTimestamp = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isUser = message.type == MessageType.user;
    final isError = message.type == MessageType.error ||
        message.status == MessageStatus.failed;
    final isSystem = message.type == MessageType.system;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
      child: Row(
        mainAxisAlignment:
            isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser && showAvatar) ...[
            _buildAvatar(context, isSystem, isError),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Column(
              crossAxisAlignment:
                  isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                _buildMessageBubble(context, theme, isUser, isError, isSystem),
                if (showTimestamp) ...[
                  const SizedBox(height: 4),
                  _buildTimestamp(context, theme, isUser),
                ],
                if (message.status == MessageStatus.failed &&
                    onRetry != null) ...[
                  const SizedBox(height: 8),
                  InlineErrorMessage(
                    message: 'Failed to send message',
                    onRetry: onRetry,
                    isCompact: true,
                  ),
                ],
              ],
            ),
          ),
          if (isUser && showAvatar) ...[
            const SizedBox(width: 8),
            _buildAvatar(context, isSystem, isError),
          ],
        ],
      ),
    );
  }

  Widget _buildAvatar(BuildContext context, bool isSystem, bool isError) {
    final theme = Theme.of(context);

    if (message.type == MessageType.user) {
      return CircleAvatar(
        radius: 16,
        backgroundColor: theme.primaryColor,
        child: Icon(
          Icons.person,
          size: 18,
          color: theme.colorScheme.onPrimary,
        ),
      );
    }

    if (isError) {
      return CircleAvatar(
        radius: 16,
        backgroundColor: theme.colorScheme.error,
        child: Icon(
          Icons.error,
          size: 18,
          color: theme.colorScheme.onError,
        ),
      );
    }

    if (isSystem) {
      return CircleAvatar(
        radius: 16,
        backgroundColor: theme.colorScheme.secondary,
        child: Icon(
          Icons.info,
          size: 18,
          color: theme.colorScheme.onSecondary,
        ),
      );
    }

    // AI Assistant avatar
    return CircleAvatar(
      radius: 16,
      backgroundColor: theme.colorScheme.primaryContainer,
      child: Icon(
        Icons.smart_toy,
        size: 18,
        color: theme.colorScheme.onPrimaryContainer,
      ),
    );
  }

  Widget _buildMessageBubble(
    BuildContext context,
    ThemeData theme,
    bool isUser,
    bool isError,
    bool isSystem,
  ) {
    final bubbleColor = _getBubbleColor(theme, isUser, isError, isSystem);
    final textColor = _getTextColor(theme, isUser, isError, isSystem);

    return GestureDetector(
      onLongPress: () => _copyToClipboard(context),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
          minWidth: 48,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: bubbleColor,
          borderRadius: _getBorderRadius(isUser),
          boxShadow: [
            BoxShadow(
              offset: const Offset(0, 1),
              blurRadius: 3,
              color: Colors.black.withValues(alpha: 0.1),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildMessageContent(context, textColor),
            if (message.status == MessageStatus.sending)
              _buildSendingIndicator(theme, textColor),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageContent(BuildContext context, Color textColor) {
    final content = message.content;
    final metadata = message.metadata;

    // Check if message has structured data (tables, references, etc.)
    if (metadata != null && metadata.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (content.isNotEmpty) ...[
            _buildText(content, textColor),
            if (metadata.containsKey('reference') ||
                metadata.containsKey('param'))
              const SizedBox(height: 8),
          ],
          if (metadata.containsKey('reference'))
            _buildReferenceData(context, metadata['reference'], textColor),
          if (metadata.containsKey('param'))
            _buildParameterData(context, metadata['param'], textColor),
        ],
      );
    }

    return _buildText(content, textColor);
  }

  Widget _buildText(String text, Color textColor) {
    return SelectableText(
      text,
      style: TextStyle(
        color: textColor,
        fontSize: 16,
        height: 1.4,
      ),
    );
  }

  Widget _buildReferenceData(
      BuildContext context, dynamic reference, Color textColor) {
    if (reference == null || reference is! Map) return const SizedBox.shrink();

    final refMap = Map<String, dynamic>.from(reference);
    if (refMap.isEmpty) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.black.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Reference Data',
            style: TextStyle(
              color: textColor,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...refMap.entries.map((entry) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 1,
                      child: Text(
                        '${entry.key}:',
                        style: TextStyle(
                          color: textColor.withValues(alpha: 0.7),
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      flex: 2,
                      child: Text(
                        entry.value?.toString() ?? 'N/A',
                        style: TextStyle(
                          color: textColor,
                          fontSize: 13,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildParameterData(
      BuildContext context, dynamic param, Color textColor) {
    if (param == null || param is! List) return const SizedBox.shrink();

    final paramList = List.from(param);
    if (paramList.isEmpty) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.black.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Parameters',
            style: TextStyle(
              color: textColor,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...paramList.asMap().entries.map((entry) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${entry.key + 1}. ',
                      style: TextStyle(
                        color: textColor.withValues(alpha: 0.7),
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        entry.value?.toString() ?? 'N/A',
                        style: TextStyle(
                          color: textColor,
                          fontSize: 13,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildSendingIndicator(ThemeData theme, Color textColor) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 12,
            height: 12,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                textColor.withValues(alpha: 0.7),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            'Sending...',
            style: TextStyle(
              color: textColor.withValues(alpha: 0.7),
              fontSize: 12,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimestamp(BuildContext context, ThemeData theme, bool isUser) {
    final timestamp = message.timestamp;
    final timeString =
        '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';

    return Text(
      timeString,
      style: TextStyle(
        color: theme.textTheme.bodySmall?.color?.withValues(alpha: 0.6),
        fontSize: 11,
      ),
    );
  }

  Color _getBubbleColor(
      ThemeData theme, bool isUser, bool isError, bool isSystem) {
    if (isError) {
      return theme.colorScheme.errorContainer;
    }

    if (isSystem) {
      return theme.colorScheme.secondaryContainer;
    }

    if (isUser) {
      return theme.colorScheme.primary;
    }

    return theme.colorScheme.surfaceContainerHighest;
  }

  Color _getTextColor(
      ThemeData theme, bool isUser, bool isError, bool isSystem) {
    if (isError) {
      return theme.colorScheme.onErrorContainer;
    }

    if (isSystem) {
      return theme.colorScheme.onSecondaryContainer;
    }

    if (isUser) {
      return theme.colorScheme.onPrimary;
    }

    return theme.colorScheme.onSurfaceVariant;
  }

  BorderRadius _getBorderRadius(bool isUser) {
    if (isUser) {
      return const BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(4),
        bottomLeft: Radius.circular(20),
        bottomRight: Radius.circular(20),
      );
    } else {
      return const BorderRadius.only(
        topLeft: Radius.circular(4),
        topRight: Radius.circular(20),
        bottomLeft: Radius.circular(20),
        bottomRight: Radius.circular(20),
      );
    }
  }

  void _copyToClipboard(BuildContext context) {
    Clipboard.setData(ClipboardData(text: message.content));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Message copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
