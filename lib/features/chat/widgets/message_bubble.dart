import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/message.dart';
import 'error_message.dart';

/// Widget for displaying individual chat messages in bubble format
class MessageBubble extends StatelessWidget {
  final Message message;
  final VoidCallback? onRetry;
  final bool showAvatar;
  final bool showTimestamp;

  const MessageBubble({
    Key? key,
    required this.message,
    this.onRetry,
    this.showAvatar = true,
    this.showTimestamp = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isUser = message.type == MessageType.user;
    final isError = message.type == MessageType.error ||
        message.status == MessageStatus.failed;
    final isSystem = message.type == MessageType.system;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
      child: Row(
        mainAxisAlignment:
            isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser && showAvatar) ...[
            _buildAvatar(context, isSystem, isError),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Column(
              crossAxisAlignment:
                  isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                _buildMessageBubble(context, theme, isUser, isError, isSystem),
                if (showTimestamp) ...[
                  const SizedBox(height: 4),
                  _buildTimestamp(context, theme, isUser),
                ],
                if (message.status == MessageStatus.failed &&
                    onRetry != null) ...[
                  const SizedBox(height: 8),
                  InlineErrorMessage(
                    message: 'Failed to send message',
                    onRetry: onRetry,
                    isCompact: true,
                  ),
                ],
              ],
            ),
          ),
          if (isUser && showAvatar) ...[
            const SizedBox(width: 8),
            _buildAvatar(context, isSystem, isError),
          ],
        ],
      ),
    );
  }

  Widget _buildAvatar(BuildContext context, bool isSystem, bool isError) {
    final theme = Theme.of(context);

    if (message.type == MessageType.user) {
      return CircleAvatar(
        radius: 16,
        backgroundColor: theme.primaryColor,
        child: Icon(
          Icons.person,
          size: 18,
          color: theme.colorScheme.onPrimary,
        ),
      );
    }

    if (isError) {
      return CircleAvatar(
        radius: 16,
        backgroundColor: theme.colorScheme.error,
        child: Icon(
          Icons.error,
          size: 18,
          color: theme.colorScheme.onError,
        ),
      );
    }

    if (isSystem) {
      return CircleAvatar(
        radius: 16,
        backgroundColor: theme.colorScheme.secondary,
        child: Icon(
          Icons.info,
          size: 18,
          color: theme.colorScheme.onSecondary,
        ),
      );
    }

    // AI Assistant avatar
    return CircleAvatar(
      radius: 16,
      backgroundColor: theme.colorScheme.primaryContainer,
      child: Icon(
        Icons.smart_toy,
        size: 18,
        color: theme.colorScheme.onPrimaryContainer,
      ),
    );
  }

  Widget _buildMessageBubble(
    BuildContext context,
    ThemeData theme,
    bool isUser,
    bool isError,
    bool isSystem,
  ) {
    final bubbleColor = _getBubbleColor(theme, isUser, isError, isSystem);
    final textColor = _getTextColor(theme, isUser, isError, isSystem);

    return GestureDetector(
      onLongPress: () => _copyToClipboard(context),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
          minWidth: 48,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: bubbleColor,
          borderRadius: _getBorderRadius(isUser),
          boxShadow: [
            BoxShadow(
              offset: const Offset(0, 1),
              blurRadius: 3,
              color: Colors.black.withValues(alpha: 0.1),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildMessageContent(context, textColor),
            if (message.status == MessageStatus.sending)
              _buildSendingIndicator(theme, textColor),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageContent(BuildContext context, Color textColor) {
    final content = message.content;
    final metadata = message.metadata;

    // Check if message has structured data (tables, references, etc.)
    if (metadata != null && metadata.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (content.isNotEmpty) ...[
            _buildText(content, textColor),
            if (metadata.containsKey('table') ||
                metadata.containsKey('reference') ||
                metadata.containsKey('param'))
              const SizedBox(height: 8),
          ],
          if (metadata.containsKey('table'))
            _buildTableData(context, metadata['table'], textColor),
          if (metadata.containsKey('reference'))
            _buildReferenceData(context, metadata['reference'], textColor),
          if (metadata.containsKey('param'))
            _buildParameterData(context, metadata['param'], textColor),
        ],
      );
    }

    return _buildText(content, textColor);
  }

  Widget _buildText(String text, Color textColor) {
    // Enhanced text formatting with better wrapping and formatting
    return SelectableText(
      text,
      style: TextStyle(
        color: textColor,
        fontSize: 16,
        height: 1.4,
        letterSpacing: 0.1,
      ),
      textAlign: TextAlign.left,
      // Enable text selection and copying
      enableInteractiveSelection: true,
      // Better text wrapping
      textWidthBasis: TextWidthBasis.parent,
    );
  }

  Widget _buildReferenceData(
      BuildContext context, dynamic reference, Color textColor) {
    if (reference == null || reference is! Map) return const SizedBox.shrink();

    final refMap = Map<String, dynamic>.from(reference);
    if (refMap.isEmpty) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.black.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Reference Data',
            style: TextStyle(
              color: textColor,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...refMap.entries.map((entry) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 1,
                      child: Text(
                        '${entry.key}:',
                        style: TextStyle(
                          color: textColor.withValues(alpha: 0.7),
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      flex: 2,
                      child: Text(
                        entry.value?.toString() ?? 'N/A',
                        style: TextStyle(
                          color: textColor,
                          fontSize: 13,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildParameterData(
      BuildContext context, dynamic param, Color textColor) {
    if (param == null || param is! List) return const SizedBox.shrink();

    final paramList = List.from(param);
    if (paramList.isEmpty) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.black.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Parameters',
            style: TextStyle(
              color: textColor,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...paramList.asMap().entries.map((entry) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${entry.key + 1}. ',
                      style: TextStyle(
                        color: textColor.withValues(alpha: 0.7),
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        entry.value?.toString() ?? 'N/A',
                        style: TextStyle(
                          color: textColor,
                          fontSize: 13,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildTableData(BuildContext context, dynamic table, Color textColor) {
    if (table == null) return const SizedBox.shrink();

    // Handle different table formats
    if (table is List && table.isNotEmpty) {
      return _buildTableFromList(context, table, textColor);
    } else if (table is Map) {
      return _buildTableFromMap(context, table, textColor);
    }

    return const SizedBox.shrink();
  }

  Widget _buildTableFromList(BuildContext context, List tableData, Color textColor) {
    if (tableData.isEmpty) return const SizedBox.shrink();

    // Check if it's a list of maps (rows with columns)
    if (tableData.first is Map) {
      return _buildDataTable(context, tableData.cast<Map<String, dynamic>>(), textColor);
    }

    // Simple list display
    return _buildSimpleList(context, tableData, textColor);
  }

  Widget _buildTableFromMap(BuildContext context, Map tableData, Color textColor) {
    if (tableData.isEmpty) return const SizedBox.shrink();

    // Check if it has headers and rows structure
    if (tableData.containsKey('headers') && tableData.containsKey('rows')) {
      return _buildStructuredTable(context, tableData, textColor);
    }

    // Treat as key-value pairs
    return _buildKeyValueTable(context, tableData, textColor);
  }

  Widget _buildDataTable(BuildContext context, List<Map<String, dynamic>> rows, Color textColor) {
    if (rows.isEmpty) return const SizedBox.shrink();

    // Get all unique keys from all rows for columns
    final Set<String> allKeys = {};
    for (final row in rows) {
      allKeys.addAll(row.keys);
    }
    final columns = allKeys.toList()..sort();

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.02),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.black.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Table header
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(7),
                topRight: Radius.circular(7),
              ),
            ),
            child: Text(
              'Table Data',
              style: TextStyle(
                color: textColor,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          // Table content
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: DataTable(
              columnSpacing: 16,
              horizontalMargin: 12,
              headingRowHeight: 40,
              dataRowHeight: 36,
              columns: columns.map((column) => DataColumn(
                label: Text(
                  column,
                  style: TextStyle(
                    color: textColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              )).toList(),
              rows: rows.map((row) => DataRow(
                cells: columns.map((column) => DataCell(
                  Container(
                    constraints: const BoxConstraints(maxWidth: 120),
                    child: Text(
                      row[column]?.toString() ?? '',
                      style: TextStyle(
                        color: textColor,
                        fontSize: 12,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ),
                )).toList(),
              )).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleList(BuildContext context, List listData, Color textColor) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.black.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'List Data',
            style: TextStyle(
              color: textColor,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...listData.asMap().entries.map((entry) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${entry.key + 1}. ',
                      style: TextStyle(
                        color: textColor.withValues(alpha: 0.7),
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        entry.value?.toString() ?? 'N/A',
                        style: TextStyle(
                          color: textColor,
                          fontSize: 13,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildStructuredTable(BuildContext context, Map tableData, Color textColor) {
    final headers = tableData['headers'] as List?;
    final rows = tableData['rows'] as List?;

    if (headers == null || rows == null || headers.isEmpty || rows.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.02),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.black.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Table header
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(7),
                topRight: Radius.circular(7),
              ),
            ),
            child: Text(
              'Structured Table',
              style: TextStyle(
                color: textColor,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          // Table content
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: DataTable(
              columnSpacing: 16,
              horizontalMargin: 12,
              headingRowHeight: 40,
              dataRowHeight: 36,
              columns: headers.map((header) => DataColumn(
                label: Text(
                  header.toString(),
                  style: TextStyle(
                    color: textColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              )).toList(),
              rows: rows.map((row) {
                final rowList = row is List ? row : [row];
                return DataRow(
                  cells: List.generate(headers.length, (index) {
                    final cellValue = index < rowList.length ? rowList[index] : '';
                    return DataCell(
                      Container(
                        constraints: const BoxConstraints(maxWidth: 120),
                        child: Text(
                          cellValue?.toString() ?? '',
                          style: TextStyle(
                            color: textColor,
                            fontSize: 12,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                        ),
                      ),
                    );
                  }),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKeyValueTable(BuildContext context, Map tableData, Color textColor) {
    if (tableData.isEmpty) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.black.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Key-Value Table',
            style: TextStyle(
              color: textColor,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...tableData.entries.map((entry) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 1,
                      child: Text(
                        '${entry.key}:',
                        style: TextStyle(
                          color: textColor.withValues(alpha: 0.7),
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      flex: 2,
                      child: Text(
                        entry.value?.toString() ?? 'N/A',
                        style: TextStyle(
                          color: textColor,
                          fontSize: 13,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildSendingIndicator(ThemeData theme, Color textColor) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 12,
            height: 12,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                textColor.withValues(alpha: 0.7),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            'Sending...',
            style: TextStyle(
              color: textColor.withValues(alpha: 0.7),
              fontSize: 12,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimestamp(BuildContext context, ThemeData theme, bool isUser) {
    final timestamp = message.timestamp;
    final timeString =
        '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';

    return Text(
      timeString,
      style: TextStyle(
        color: theme.textTheme.bodySmall?.color?.withValues(alpha: 0.6),
        fontSize: 11,
      ),
    );
  }

  Color _getBubbleColor(
      ThemeData theme, bool isUser, bool isError, bool isSystem) {
    if (isError) {
      return theme.colorScheme.errorContainer;
    }

    if (isSystem) {
      return theme.colorScheme.secondaryContainer;
    }

    if (isUser) {
      return theme.colorScheme.primary;
    }

    return theme.colorScheme.surfaceContainerHighest;
  }

  Color _getTextColor(
      ThemeData theme, bool isUser, bool isError, bool isSystem) {
    if (isError) {
      return theme.colorScheme.onErrorContainer;
    }

    if (isSystem) {
      return theme.colorScheme.onSecondaryContainer;
    }

    if (isUser) {
      return theme.colorScheme.onPrimary;
    }

    return theme.colorScheme.onSurfaceVariant;
  }

  BorderRadius _getBorderRadius(bool isUser) {
    if (isUser) {
      return const BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(4),
        bottomLeft: Radius.circular(20),
        bottomRight: Radius.circular(20),
      );
    } else {
      return const BorderRadius.only(
        topLeft: Radius.circular(4),
        topRight: Radius.circular(20),
        bottomLeft: Radius.circular(20),
        bottomRight: Radius.circular(20),
      );
    }
  }

  void _copyToClipboard(BuildContext context) {
    Clipboard.setData(ClipboardData(text: message.content));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Message copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
