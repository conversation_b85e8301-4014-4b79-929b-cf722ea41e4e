import 'package:flutter/foundation.dart';
import '../models/message.dart';
import '../models/chat_session.dart';
import '../repositories/chat_repository.dart';
import '../../../config/chat_config.dart';

/// State management provider for chat functionality
class ChatProvider extends ChangeNotifier {
  final ChatRepository _repository;
  
  // Core state
  List<Message> _messages = [];
  ChatSession? _currentSession;
  bool _isLoading = false;
  bool _isSending = false;
  String? _error;

  // Session management
  String? _sessionId;
  final String _agentId;

  ChatProvider({
    required ChatRepository repository,
    String? agentId,
  })  : _repository = repository,
        _agentId = agentId ?? ChatConfig.defaultAgentId;

  // Getters
  List<Message> get messages => List.unmodifiable(_messages);
  ChatSession? get currentSession => _currentSession;
  bool get isLoading => _isLoading;
  bool get isSending => _isSending;
  String? get error => _error;
  String? get sessionId => _sessionId;
  String get agentId => _agentId;
  bool get hasSession => _sessionId != null;
  bool get hasMessages => _messages.isNotEmpty;
  bool get isInitialized => _currentSession != null;

  /// Initialize a new chat session
  Future<void> initializeSession() async {
    if (_isLoading) return;

    _setLoading(true);
    _clearError();

    try {
      final response = await _repository.initializeSession(_agentId);
      
      if (response.isSuccess && response.data.sessionId.isNotEmpty) {
        _sessionId = response.data.sessionId;
        
        // Create session object
        _currentSession = ChatSession(
          sessionId: _sessionId!,
          agentId: _agentId,
        );

        // Add welcome message if present
        if (response.data.answer.isNotEmpty) {
          final welcomeMessage = Message(
            content: response.data.answer,
            type: MessageType.assistant,
            metadata: {
              'messageId': response.data.id,
              'reference': response.data.reference,
              'param': response.data.param,
            },
          );
          _addMessage(welcomeMessage);
        }
      } else {
        throw Exception('Failed to initialize session: ${response.message}');
      }
    } catch (e) {
      _setError('Failed to initialize session: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Send a message to the AI agent
  Future<void> sendMessage(String content) async {
    if (_isSending || content.trim().isEmpty || !hasSession) return;

    _setSending(true);
    _clearError();

    // Create user message
    final userMessage = Message(
      content: content.trim(),
      type: MessageType.user,
      status: MessageStatus.sending,
    );
    
    _addMessage(userMessage);

    try {
      final response = await _repository.sendMessage(
        agentId: _agentId,
        message: content.trim(),
        sessionId: _sessionId!,
      );

      // Update user message status to sent
      _updateMessageStatus(userMessage.id, MessageStatus.sent);

      if (response.isSuccess && response.data.answer.isNotEmpty) {
        // Create AI response message
        final aiMessage = Message(
          content: response.data.answer,
          type: MessageType.assistant,
          metadata: {
            'messageId': response.data.id,
            'reference': response.data.reference,
            'param': response.data.param,
          },
        );
        _addMessage(aiMessage);
      } else {
        throw Exception('Invalid response: ${response.message}');
      }
    } catch (e) {
      // Update user message status to failed
      _updateMessageStatus(userMessage.id, MessageStatus.failed);
      _setError('Failed to send message: ${e.toString()}');
    } finally {
      _setSending(false);
    }
  }

  /// Retry sending a failed message
  Future<void> retryMessage(String messageId) async {
    final message = _messages.firstWhere(
      (m) => m.id == messageId,
      orElse: () => throw ArgumentError('Message not found: $messageId'),
    );

    if (message.status != MessageStatus.failed || message.type != MessageType.user) {
      return;
    }

    if (_isSending || !hasSession) return;

    _setSending(true);
    _clearError();

    // Update message status to sending
    _updateMessageStatus(messageId, MessageStatus.sending);

    try {
      final response = await _repository.sendMessage(
        agentId: _agentId,
        message: message.content,
        sessionId: _sessionId!,
      );

      // Update user message status to sent
      _updateMessageStatus(messageId, MessageStatus.sent);

      if (response.isSuccess && response.data.answer.isNotEmpty) {
        // Create AI response message
        final aiMessage = Message(
          content: response.data.answer,
          type: MessageType.assistant,
          metadata: {
            'messageId': response.data.id,
            'reference': response.data.reference,
            'param': response.data.param,
          },
        );
        _addMessage(aiMessage);
      } else {
        throw Exception('Invalid response: ${response.message}');
      }
    } catch (e) {
      // Update user message status back to failed
      _updateMessageStatus(messageId, MessageStatus.failed);
      _setError('Failed to retry message: ${e.toString()}');
    } finally {
      _setSending(false);
    }
  }

  /// Clear all messages and reset session
  Future<void> startNewSession() async {
    await clearSession();
    await initializeSession();
  }

  /// Clear current session and messages
  Future<void> clearSession() async {
    if (_sessionId != null) {
      try {
        await _repository.endSession(_sessionId!);
      } catch (e) {
        // Log error but don't prevent clearing
        debugPrint('Error ending session: $e');
      }
    }

    _sessionId = null;
    _currentSession = null;
    _messages.clear();
    _clearError();
    notifyListeners();
  }

  /// Clear error message (public method)
  void clearError() {
    _clearError();
  }

  /// Add a message to the conversation
  void _addMessage(Message message) {
    _messages.add(message);
    
    // Update session if it exists
    if (_currentSession != null) {
      _currentSession = _currentSession!.addMessage(message);
    }
    
    notifyListeners();
  }

  /// Update message status
  void _updateMessageStatus(String messageId, MessageStatus status) {
    final index = _messages.indexWhere((m) => m.id == messageId);
    if (index != -1) {
      _messages[index] = _messages[index].copyWith(status: status);
      
      // Update session if it exists
      if (_currentSession != null) {
        _currentSession = _currentSession!.updateMessage(messageId, _messages[index]);
      }
      
      notifyListeners();
    }
  }

  /// Set loading state
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// Set sending state
  void _setSending(bool sending) {
    if (_isSending != sending) {
      _isSending = sending;
      notifyListeners();
    }
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error message
  void _clearError() {
    if (_error != null) {
      _error = null;
      notifyListeners();
    }
  }

  /// Get messages by type
  List<Message> getMessagesByType(MessageType type) {
    return _messages.where((message) => message.type == type).toList();
  }

  /// Get pending messages
  List<Message> getPendingMessages() {
    return _messages
        .where((message) => message.status == MessageStatus.sending)
        .toList();
  }

  /// Get failed messages
  List<Message> getFailedMessages() {
    return _messages
        .where((message) => message.status == MessageStatus.failed)
        .toList();
  }

  @override
  void dispose() {
    _repository.dispose();
    super.dispose();
  }
}
