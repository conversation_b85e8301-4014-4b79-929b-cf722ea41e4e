import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/chat_provider.dart';
import '../widgets/message_bubble.dart';
import '../widgets/chat_input.dart';
import '../widgets/loading_indicator.dart';
import '../models/message.dart';

/// Main chat screen interface with message list and input
class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> with WidgetsBindingObserver {
  final ScrollController _scrollController = ScrollController();
  final FocusNode _inputFocusNode = FocusNode();
  int _previousMessageCount = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Initialize session when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeSession();
    });

    // Listen for keyboard visibility changes to adjust scrolling
    _inputFocusNode.addListener(() {
      if (_inputFocusNode.hasFocus) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      }
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _scrollController.dispose();
    _inputFocusNode.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    final provider = context.read<ChatProvider>();

    switch (state) {
      case AppLifecycleState.resumed:
        // Re-initialize session if needed when app comes back to foreground
        if (!provider.hasSession && !provider.isLoading) {
          _initializeSession();
        }
        break;
      case AppLifecycleState.paused:
        // App is paused but still in memory - maintain session
        break;
      case AppLifecycleState.inactive:
        // App is inactive (e.g., during phone call) - maintain session
        break;
      case AppLifecycleState.detached:
        // App is detached - session will be cleaned up naturally
        break;
      case AppLifecycleState.hidden:
        // App is hidden - maintain session
        break;
    }
  }

  void _initializeSession() {
    final provider = context.read<ChatProvider>();
    if (!provider.hasSession && !provider.isLoading) {
      provider.initializeSession();
    }
  }

  void _scrollToBottom({bool animated = true}) {
    if (_scrollController.hasClients) {
      if (animated) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      } else {
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      }
    }
  }

  void _handleSendMessage(String message) {
    final provider = context.read<ChatProvider>();
    provider.sendMessage(message);

    // Scroll to bottom after sending message
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }

  void _handleRetryMessage(String messageId) {
    final provider = context.read<ChatProvider>();
    provider.retryMessage(messageId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI Chat'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          Consumer<ChatProvider>(
            builder: (context, provider, child) {
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Session status indicator
                  if (provider.hasSession)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.circle,
                            size: 8,
                            color: Colors.green,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Connected',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.green.shade700,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(width: 8),
                  // New session button
                  IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: provider.isLoading || provider.isSending
                        ? null
                        : () => provider.startNewSession(),
                    tooltip: 'Start New Session',
                  ),
                ],
              );
            },
          ),
        ],
      ),
      body: Consumer<ChatProvider>(
        builder: (context, provider, child) {
          // Auto-scroll when new messages arrive
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (provider.messages.length > _previousMessageCount) {
              _scrollToBottom();
              _previousMessageCount = provider.messages.length;
            }
          });

          return Column(
            children: [
              // Error banner
              if (provider.error != null && provider.hasMessages)
                _buildErrorBanner(provider),
              // Messages list
              Expanded(
                child: _buildMessagesList(provider),
              ),
              // Input area
              _buildInputArea(provider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildMessagesList(ChatProvider provider) {
    if (provider.isLoading && !provider.hasMessages) {
      return const Center(
        child: LoadingIndicator(
          type: LoadingType.circular,
          message: 'Initializing chat session...',
        ),
      );
    }

    if (provider.error != null && !provider.hasMessages) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to initialize chat',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              provider.error!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => provider.initializeSession(),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (!provider.hasMessages) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'Start a conversation',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Type a message below to begin chatting with the AI assistant.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: provider.messages.length + (provider.isSending ? 1 : 0),
      itemBuilder: (context, index) {
        // Show typing indicator if sending
        if (index == provider.messages.length && provider.isSending) {
          return const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  child: Icon(Icons.smart_toy, size: 16),
                ),
                SizedBox(width: 12),
                LoadingIndicator(
                  type: LoadingType.typing,
                  size: 32,
                ),
              ],
            ),
          );
        }

        final message = provider.messages[index];
        return MessageBubble(
          message: message,
          showTimestamp: true,
          showAvatar: true,
          onRetry: message.status == MessageStatus.failed
              ? () => _handleRetryMessage(message.id)
              : null,
        );
      },
    );
  }

  Widget _buildErrorBanner(ChatProvider provider) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      color: Theme.of(context).colorScheme.errorContainer,
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Theme.of(context).colorScheme.onErrorContainer,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              provider.error!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onErrorContainer,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              final provider = context.read<ChatProvider>();
              provider.clearError();
            },
            child: Text(
              'Dismiss',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onErrorContainer,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputArea(ChatProvider provider) {
    return ChatInput(
      onSendMessage: _handleSendMessage,
      isEnabled: provider.hasSession && !provider.isSending,
      isLoading: provider.isSending,
      placeholder: provider.hasSession
          ? 'Type your message...'
          : 'Initializing chat...',
    );
  }
}
